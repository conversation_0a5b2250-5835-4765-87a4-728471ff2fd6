build:
  excludeCorrectable: false

config:
  validation: true

processors:
  active: true
  exclude:
    - 'DetektProgressListener'

console-reports:
  active: true
  exclude:
    - 'ProjectStatisticsReport'
    - 'ComplexityReport'
    - 'NotificationReport'
    - 'FileBasedFindingsReport'

output-reports:
  active: true

comments:
  active: true
  AbsentOrWrongFileLicense:
    active: true
    licenseTemplateFile: ./license.template
    licenseTemplateIsRegex: true

complexity:
  active: false

coroutines:
  active: true
  GlobalCoroutineUsage:
    active: true
  InjectDispatcher:
    active: false
  SuspendFunWithFlowReturnType:
    active: true

empty-blocks:
  EmptyFunctionBlock:
    active: false

exceptions:
  # there might actually be some useful rules in here
  active: false

# unfortunately, does not respect .editorconfig in the project folder
# this configuration reflects the delta from the default "formatting" (ktlint) rule set
formatting:
  active: true
  android: false
  autoCorrect: true
  AnnotationOnSeparateLine:
    active: false
  AnnotationSpacing:
    active: false
  ArgumentListWrapping:
    active: true
    indentSize: 4
    maxLineLength: 160
  Indentation:
    active: true
    indentSize: 4
    excludes: [ '**/TelemetryDefinitions.kt' ]
  MaximumLineLength:
    active: true
    maxLineLength: 160
    ignoreBackTickedIdentifier: true
  NoWildcardImports:
    packagesToUseImportOnDemandProperty: ""
    active: true
  ParameterListWrapping:
    active: true
    indentSize: 4
    maxLineLength: 160
  ParameterWrapping:
    active: true
    indentSize: 4
    maxLineLength: 160
  PropertyWrapping:
    active: true
    indentSize: 4
    maxLineLength: 160
  SpacingBetweenDeclarationsWithComments:
    active: true
    excludes: [ '**/icons/**' ]
  #  TrailingCommaOnCallSite:
  #    active: true
  TrailingCommaOnDeclarationSite:
    active: true
  Wrapping:
    active: true
    indentSize: 4
    maxLineLength: 160

naming:
  active: true
  ClassNaming:
    active: true
    classPattern: '[A-Z][a-zA-Z0-9]*'
  ConstructorParameterNaming:
    active: true
    parameterPattern: '[a-z][A-Za-z0-9]*'
    privateParameterPattern: '[a-z][A-Za-z0-9]*'
    excludeClassPattern: '$^'
  EnumNaming:
    active: true
    enumEntryPattern: '[A-Z][_a-zA-Z0-9]*'
  FunctionNaming:
    active: true
    functionPattern: '([a-z][a-zA-Z0-9]*)|(`.*`)'
    excludeClassPattern: '$^'
    ignoreAnnotated: [ 'Composable' ]
  FunctionParameterNaming:
    active: true
    parameterPattern: '[a-z][A-Za-z0-9]*'
    excludeClassPattern: '$^'
  # prefer rule provided by ktlint
  MatchingDeclarationName:
    active: false
  MemberNameEqualsClassName:
    active: false
  NoNameShadowing:
    active: true
  ObjectPropertyNaming:
    active: true
    constantPattern: '[A-Za-z][_A-Za-z0-9]*'
    propertyPattern: '[A-Za-z][_A-Za-z0-9]*'
    privatePropertyPattern: '(_)?[A-Za-z][_A-Za-z0-9]*'
  PackageNaming:
    active: true
    packagePattern: '[a-z]+(\.[a-z][A-Za-z0-9]*)*'
  TopLevelPropertyNaming:
    active: true
    constantPattern: '[A-Z][_A-Z0-9]*'
    propertyPattern: '[A-Za-z][_A-Za-z0-9]*'
    privatePropertyPattern: '_?[A-Za-z][_A-Za-z0-9]*'
  VariableNaming:
    active: true
    variablePattern: '[a-z][A-Za-z0-9]*'
    privateVariablePattern: '(_)?[a-z][A-Za-z0-9]*'
    excludeClassPattern: '$^'

performance:
  SpreadOperator:
    active: false

potential-bugs:
  active: true
  CastToNullableType:
    active: false
  Deprecation:
    active: false
  DontDowncastCollectionTypes:
    active: false
  EqualsAlwaysReturnsTrueOrFalse:
    active: true
  EqualsWithHashCodeExist:
    active: true
  ExitOutsideMain:
    active: true
  ExplicitGarbageCollectionCall:
    active: true
  HasPlatformType:
    active: false
  IgnoredReturnValue:
    active: false
  ImplicitUnitReturnType:
    active: false
    allowExplicitReturnType: true
  InvalidRange:
    active: true
  IteratorHasNextCallsNextMethod:
    active: true
  IteratorNotThrowingNoSuchElementException:
    active: true
  MapGetWithNotNullAssertionOperator:
    active: false
  NullableToStringCall:
    active: false
  UnconditionalJumpStatementInLoop:
    active: false
  UnnecessaryNotNullOperator:
    active: true
  UnnecessarySafeCall:
    active: true
  UnreachableCatchBlock:
    active: true
  # doesn't seem to work correctly on our codebase
  # qodana provides coverage for this
  UnreachableCode:
    active: false
  UnsafeCallOnNullableType:
    active: true
  UnsafeCast:
    active: true
  UnusedUnaryOperator:
    active: false
  UselessPostfixExpression:
    active: false
  WrongEqualsTypeParameter:
    active: true

style:
  ExpressionBodySyntax:
    active: true
    includeLineWrapping: true
  ForbiddenComment:
    active: false
  MagicNumber:
    active: false
  # prefer rule provided by ktlint
  MaxLineLength:
    active: false
  # prefer rule provided by ktlint
  ModifierOrder:
    active: false
  # prefer rule provided by ktlint
  NewLineAtEndOfFile:
    active: false
  NoTabs:
    active: true
  ReturnCount:
    active: false
  ThrowsCount:
    active: false
  UnnecessaryAbstractClass:
    active: false
  UnusedPrivateMember:
    allowedNames: '(_|ignored|expected|serialVersionUID|createUIComponents)'
  VarCouldBeVal:
    active: false
