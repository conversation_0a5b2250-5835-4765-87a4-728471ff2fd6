<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Run AWS Toolkit - Gateway [2025.1]" type="GradleRunConfiguration" factoryName="Gradle" folderName="2025.1">
    <log_file alias="idea.log" path="$PROJECT_DIR$/plugins/toolkit/jetbrains-gateway/build/idea-sandbox/GW-2025.1/log/idea.log" />
    <ExternalSystemSettings>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value=" -PideProfileName=2025.1" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list>
          <option value=":plugin-toolkit:jetbrains-gateway:runIde" />
        </list>
      </option>
      <option name="vmOptions" />
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <RunAsTest>false</RunAsTest>
    <method v="2" />
  </configuration>
</component>