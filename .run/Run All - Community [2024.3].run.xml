<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Run All - Community [2024.3]" type="GradleRunConfiguration" factoryName="Gradle" folderName="2024.3">
    <log_file alias="idea.log" path="$PROJECT_DIR$/plugins/sandbox-all/build/idea-sandbox/IC-2024.3/log/idea.log" />
    <ExternalSystemSettings>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="-PrunIdeVariant=IC -PideProfileName=2024.3" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list>
          <option value=":sandbox-all:runIde" />
        </list>
      </option>
      <option name="vmOptions" />
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <RunAsTest>false</RunAsTest>
    <method v="2" />
  </configuration>
</component>