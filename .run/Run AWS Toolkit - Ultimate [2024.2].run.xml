<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Run AWS Toolkit - Ultimate [2024.2]" type="GradleRunConfiguration" factoryName="Gradle" folderName="2024.2">
    <log_file alias="idea.log" path="$PROJECT_DIR$/plugins/toolkit/intellij-standalone/build/idea-sandbox/IU-2024.2/log/idea.log" />
    <ExternalSystemSettings>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="-PrunIdeVariant=IU -PideProfileName=2024.2" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list>
          <option value=":plugin-toolkit:intellij-standalone:runIde" />
        </list>
      </option>
      <option name="vmOptions" />
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <RunAsTest>false</RunAsTest>
    <method v="2" />
  </configuration>
</component>