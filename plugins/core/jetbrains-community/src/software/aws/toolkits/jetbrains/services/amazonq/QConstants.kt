// Copyright 2024 Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: Apache-2.0

package software.aws.toolkits.jetbrains.services.amazonq

object QConstants {
    const val Q_MARKETPLACE_URI = "https://aws.amazon.com/q/developer/"
    const val CODEWHISPERER_LOGIN_HELP_URI = "https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/auth-access.html"
    const val MAX_FILE_SIZE_BYTES = 1024 * 1024
}
