{"version": "2.0", "metadata": {"apiVersion": "2022-11-11", "auth": ["smithy.api#httpBearerAuth"], "endpointPrefix": "amazoncodewhispererservice", "jsonVersion": "1.0", "protocol": "json", "protocols": ["json"], "serviceFullName": "Amazon CodeWhisperer", "serviceId": "CodeWhispererRuntime", "signatureVersion": "bearer", "signingName": "amazoncodewhispererservice", "targetPrefix": "AmazonCodeWhispererService", "uid": "codewhispererruntime-2022-11-11"}, "operations": {"CreateArtifactUploadUrl": {"name": "CreateArtifactUploadUrl", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateUploadUrlRequest"}, "output": {"shape": "CreateUploadUrlResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "idempotent": true}, "CreateTaskAssistConversation": {"name": "CreateTaskAssistConversation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateTaskAssistConversationRequest"}, "output": {"shape": "CreateTaskAssistConversationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "CreateUploadUrl": {"name": "CreateUploadUrl", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateUploadUrlRequest"}, "output": {"shape": "CreateUploadUrlResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "idempotent": true}, "CreateWorkspace": {"name": "CreateWorkspace", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateWorkspaceRequest"}, "output": {"shape": "CreateWorkspaceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "DeleteTaskAssistConversation": {"name": "DeleteTaskAssistConversation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteTaskAssistConversationRequest"}, "output": {"shape": "DeleteTaskAssistConversationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "DeleteWorkspace": {"name": "DeleteWorkspace", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteWorkspaceRequest"}, "output": {"shape": "DeleteWorkspaceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "GenerateCompletions": {"name": "GenerateCompletions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GenerateCompletionsRequest"}, "output": {"shape": "GenerateCompletionsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "GetCodeAnalysis": {"name": "GetCodeAnalysis", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCodeAnalysisRequest"}, "output": {"shape": "GetCodeAnalysisResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "GetCodeFixJob": {"name": "GetCodeFixJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCodeFixJobRequest"}, "output": {"shape": "GetCodeFixJobResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "GetTaskAssistCodeGeneration": {"name": "GetTaskAssistCodeGeneration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTaskAssistCodeGenerationRequest"}, "output": {"shape": "GetTaskAssistCodeGenerationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "GetTestGeneration": {"name": "GetTestGeneration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTestGenerationRequest"}, "output": {"shape": "GetTestGenerationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "GetTransformation": {"name": "GetTransformation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTransformationRequest"}, "output": {"shape": "GetTransformationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "GetTransformationPlan": {"name": "GetTransformationPlan", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTransformationPlanRequest"}, "output": {"shape": "GetTransformationPlanResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "ListAvailableCustomizations": {"name": "ListAvailableCustomizations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAvailableCustomizationsRequest"}, "output": {"shape": "ListAvailableCustomizationsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "ListAvailableProfiles": {"name": "ListAvailableProfiles", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAvailableProfilesRequest"}, "output": {"shape": "ListAvailableProfilesResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "ListCodeAnalysisFindings": {"name": "ListCodeAnalysisFindings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCodeAnalysisFindingsRequest"}, "output": {"shape": "ListCodeAnalysisFindingsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "ListEvents": {"name": "ListEvents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEventsRequest"}, "output": {"shape": "ListEventsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "ListFeatureEvaluations": {"name": "ListFeatureEvaluations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFeatureEvaluationsRequest"}, "output": {"shape": "ListFeatureEvaluationsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "ListWorkspaceMetadata": {"name": "ListWorkspaceMetadata", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListWorkspaceMetadataRequest"}, "output": {"shape": "ListWorkspaceMetadataResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "ResumeTransformation": {"name": "ResumeTransformation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ResumeTransformationRequest"}, "output": {"shape": "ResumeTransformationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "SendTelemetryEvent": {"name": "SendTelemetryEvent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SendTelemetryEventRequest"}, "output": {"shape": "SendTelemetryEventResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "idempotent": true}, "StartCodeAnalysis": {"name": "StartCodeAnalysis", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartCodeAnalysisRequest"}, "output": {"shape": "StartCodeAnalysisResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "idempotent": true}, "StartCodeFixJob": {"name": "StartCodeFixJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartCodeFixJobRequest"}, "output": {"shape": "StartCodeFixJobResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "StartTaskAssistCodeGeneration": {"name": "StartTaskAssistCodeGeneration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartTaskAssistCodeGenerationRequest"}, "output": {"shape": "StartTaskAssistCodeGenerationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "StartTestGeneration": {"name": "StartTestGeneration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartTestGenerationRequest"}, "output": {"shape": "StartTestGenerationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "idempotent": true}, "StartTransformation": {"name": "StartTransformation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartTransformationRequest"}, "output": {"shape": "StartTransformationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}, "StopTransformation": {"name": "StopTransformation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopTransformationRequest"}, "output": {"shape": "StopTransformationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}]}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "reason": {"shape": "AccessDeniedExceptionReason"}}, "exception": true}, "AccessDeniedExceptionReason": {"type": "string", "enum": ["UNAUTHORIZED_CUSTOMIZATION_RESOURCE_ACCESS"]}, "ActiveFunctionalityList": {"type": "list", "member": {"shape": "FunctionalityName"}, "max": 10, "min": 0}, "AdditionalContentEntry": {"type": "structure", "required": ["name", "description"], "members": {"name": {"shape": "AdditionalContentEntryNameString"}, "description": {"shape": "AdditionalContentEntryDescriptionString"}, "innerContext": {"shape": "AdditionalContentEntryInnerContextString"}}}, "AdditionalContentEntryDescriptionString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "AdditionalContentEntryInnerContextString": {"type": "string", "max": 8192, "min": 1, "sensitive": true}, "AdditionalContentEntryNameString": {"type": "string", "max": 1024, "min": 1, "pattern": "[a-z]+(?:-[a-z0-9]+)*", "sensitive": true}, "AdditionalContentList": {"type": "list", "member": {"shape": "AdditionalContentEntry"}, "max": 20, "min": 0}, "AppStudioState": {"type": "structure", "required": ["namespace", "propertyName", "propertyContext"], "members": {"namespace": {"shape": "AppStudioStateNamespaceString"}, "propertyName": {"shape": "AppStudioStatePropertyNameString"}, "propertyValue": {"shape": "AppStudioStatePropertyValueString"}, "propertyContext": {"shape": "AppStudioStatePropertyContextString"}}}, "AppStudioStateNamespaceString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "AppStudioStatePropertyContextString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "AppStudioStatePropertyNameString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "AppStudioStatePropertyValueString": {"type": "string", "max": 10240, "min": 0, "sensitive": true}, "ApplicationProperties": {"type": "structure", "required": ["tenantId", "applicationArn", "tenantUrl", "applicationType"], "members": {"tenantId": {"shape": "TenantId"}, "applicationArn": {"shape": "ResourceArn"}, "tenantUrl": {"shape": "Url"}, "applicationType": {"shape": "FunctionalityName"}}}, "ApplicationPropertiesList": {"type": "list", "member": {"shape": "ApplicationProperties"}}, "ArtifactId": {"type": "string", "max": 126, "min": 1, "pattern": "[a-zA-Z0-9-_]+"}, "ArtifactMap": {"type": "map", "key": {"shape": "ArtifactType"}, "value": {"shape": "UploadId"}, "max": 64, "min": 1}, "ArtifactType": {"type": "string", "enum": ["SourceCode", "BuiltJars"]}, "AssistantResponseMessage": {"type": "structure", "required": ["content"], "members": {"messageId": {"shape": "MessageId"}, "content": {"shape": "AssistantResponseMessageContentString"}, "supplementaryWebLinks": {"shape": "SupplementaryWebLinks"}, "references": {"shape": "References"}, "followupPrompt": {"shape": "FollowupPrompt"}, "toolUses": {"shape": "ToolUses"}}}, "AssistantResponseMessageContentString": {"type": "string", "max": 100000, "min": 0, "sensitive": true}, "Base64EncodedPaginationToken": {"type": "string", "max": 2048, "min": 1, "pattern": "(?:[A-Za-z0-9\\+/]{4})*(?:[A-Za-z0-9\\+/]{2}\\=\\=|[A-Za-z0-9\\+/]{3}\\=)?"}, "Boolean": {"type": "boolean", "box": true}, "ByUserAnalytics": {"type": "structure", "required": ["toggle"], "members": {"s3Uri": {"shape": "S3Uri"}, "toggle": {"shape": "OptInFeatureToggle"}}}, "ChatAddMessageEvent": {"type": "structure", "required": ["conversationId", "messageId"], "members": {"conversationId": {"shape": "ConversationId"}, "messageId": {"shape": "MessageId"}, "customizationArn": {"shape": "CustomizationArn"}, "userIntent": {"shape": "UserIntent"}, "hasCodeSnippet": {"shape": "Boolean"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "activeEditorTotalCharacters": {"shape": "Integer"}, "timeToFirstChunkMilliseconds": {"shape": "Double"}, "timeBetweenChunks": {"shape": "timeBetweenChunks"}, "fullResponselatency": {"shape": "Double"}, "requestLength": {"shape": "Integer"}, "responseLength": {"shape": "Integer"}, "numberOfCodeBlocks": {"shape": "Integer"}, "hasProjectLevelContext": {"shape": "Boolean"}}}, "ChatHistory": {"type": "list", "member": {"shape": "ChatMessage"}, "max": 100, "min": 0}, "ChatInteractWithMessageEvent": {"type": "structure", "required": ["conversationId", "messageId"], "members": {"conversationId": {"shape": "ConversationId"}, "messageId": {"shape": "MessageId"}, "customizationArn": {"shape": "CustomizationArn"}, "interactionType": {"shape": "ChatMessageInteractionType"}, "interactionTarget": {"shape": "ChatInteractWithMessageEventInteractionTargetString"}, "acceptedCharacterCount": {"shape": "Integer"}, "acceptedLineCount": {"shape": "Integer"}, "acceptedSnippetHasReference": {"shape": "Boolean"}, "hasProjectLevelContext": {"shape": "Boolean"}, "userIntent": {"shape": "UserIntent"}, "addedIdeDiagnostics": {"shape": "IdeDiagnosticList"}, "removedIdeDiagnostics": {"shape": "IdeDiagnosticList"}}}, "ChatInteractWithMessageEventInteractionTargetString": {"type": "string", "max": 1024, "min": 1}, "ChatMessage": {"type": "structure", "members": {"userInputMessage": {"shape": "UserInputMessage"}, "assistantResponseMessage": {"shape": "AssistantResponseMessage"}}, "union": true}, "ChatMessageInteractionType": {"type": "string", "enum": ["INSERT_AT_CURSOR", "COPY_SNIPPET", "COPY", "CLICK_LINK", "CLICK_BODY_LINK", "CLICK_FOLLOW_UP", "HOVER_REFERENCE", "UPVOTE", "DOWNVOTE"]}, "ChatTriggerType": {"type": "string", "enum": ["MANUAL", "DIAGNOSTIC", "INLINE_CHAT"]}, "ChatUserModificationEvent": {"type": "structure", "required": ["conversationId", "messageId", "modificationPercentage"], "members": {"conversationId": {"shape": "ConversationId"}, "customizationArn": {"shape": "CustomizationArn"}, "messageId": {"shape": "MessageId"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "modificationPercentage": {"shape": "Double"}, "hasProjectLevelContext": {"shape": "Boolean"}}}, "ClientId": {"type": "string", "max": 255, "min": 1}, "CodeAnalysisFindingsSchema": {"type": "string", "enum": ["codeanalysis/findings/1.0"]}, "CodeAnalysisScope": {"type": "string", "enum": ["FILE", "PROJECT"]}, "CodeAnalysisStatus": {"type": "string", "enum": ["Completed", "Pending", "Failed"]}, "CodeAnalysisUploadContext": {"type": "structure", "required": ["codeScanName"], "members": {"codeScanName": {"shape": "CodeScanName"}}}, "CodeCoverageEvent": {"type": "structure", "required": ["programmingLanguage", "acceptedCharacterCount", "totalCharacterCount", "timestamp"], "members": {"customizationArn": {"shape": "CustomizationArn"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "acceptedCharacterCount": {"shape": "PrimitiveInteger"}, "totalCharacterCount": {"shape": "PrimitiveInteger"}, "timestamp": {"shape": "Timestamp"}, "unmodifiedAcceptedCharacterCount": {"shape": "PrimitiveInteger"}, "totalNewCodeCharacterCount": {"shape": "PrimitiveInteger"}, "totalNewCodeLineCount": {"shape": "PrimitiveInteger"}, "userWrittenCodeCharacterCount": {"shape": "CodeCoverageEventUserWrittenCodeCharacterCountInteger"}, "userWrittenCodeLineCount": {"shape": "CodeCoverageEventUserWrittenCodeLineCountInteger"}}}, "CodeCoverageEventUserWrittenCodeCharacterCountInteger": {"type": "integer", "min": 0}, "CodeCoverageEventUserWrittenCodeLineCountInteger": {"type": "integer", "min": 0}, "CodeFixAcceptanceEvent": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "String"}, "ruleId": {"shape": "String"}, "detectorId": {"shape": "String"}, "findingId": {"shape": "String"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "linesOfCodeAccepted": {"shape": "Integer"}, "charsOfCodeAccepted": {"shape": "Integer"}}}, "CodeFixGenerationEvent": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "String"}, "ruleId": {"shape": "String"}, "detectorId": {"shape": "String"}, "findingId": {"shape": "String"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "linesOfCodeGenerated": {"shape": "Integer"}, "charsOfCodeGenerated": {"shape": "Integer"}}}, "CodeFixJobStatus": {"type": "string", "enum": ["Succeeded", "InProgress", "Failed"]}, "CodeFixName": {"type": "string", "max": 128, "min": 1, "pattern": "[a-zA-Z0-9-_$:.]*"}, "CodeFixUploadContext": {"type": "structure", "required": ["codeFixName"], "members": {"codeFixName": {"shape": "CodeFixName"}}}, "CodeGenerationId": {"type": "string", "max": 128, "min": 1}, "CodeGenerationStatus": {"type": "structure", "required": ["status", "currentStage"], "members": {"status": {"shape": "CodeGenerationWorkflowStatus"}, "currentStage": {"shape": "CodeGenerationWorkflowStage"}}}, "CodeGenerationStatusDetail": {"type": "string", "sensitive": true}, "CodeGenerationWorkflowStage": {"type": "string", "enum": ["InitialCodeGeneration", "CodeRefinement"]}, "CodeGenerationWorkflowStatus": {"type": "string", "enum": ["InProgress", "Complete", "Failed"]}, "CodeScanEvent": {"type": "structure", "required": ["programmingLanguage", "codeScanJobId", "timestamp"], "members": {"programmingLanguage": {"shape": "ProgrammingLanguage"}, "codeScanJobId": {"shape": "CodeScanJobId"}, "timestamp": {"shape": "Timestamp"}, "codeAnalysisScope": {"shape": "CodeAnalysisScope"}}}, "CodeScanFailedEvent": {"type": "structure", "required": ["programmingLanguage", "codeScanJobId", "timestamp"], "members": {"programmingLanguage": {"shape": "ProgrammingLanguage"}, "codeScanJobId": {"shape": "CodeScanJobId"}, "timestamp": {"shape": "Timestamp"}, "codeAnalysisScope": {"shape": "CodeAnalysisScope"}}}, "CodeScanJobId": {"type": "string", "max": 128, "min": 1}, "CodeScanName": {"type": "string", "max": 128, "min": 1}, "CodeScanRemediationsEvent": {"type": "structure", "members": {"programmingLanguage": {"shape": "ProgrammingLanguage"}, "CodeScanRemediationsEventType": {"shape": "CodeScanRemediationsEventType"}, "timestamp": {"shape": "Timestamp"}, "detectorId": {"shape": "String"}, "findingId": {"shape": "String"}, "ruleId": {"shape": "String"}, "component": {"shape": "String"}, "reason": {"shape": "String"}, "result": {"shape": "String"}, "includesFix": {"shape": "Boolean"}}}, "CodeScanRemediationsEventType": {"type": "string", "enum": ["CODESCAN_ISSUE_HOVER", "CODESCAN_ISSUE_APPLY_FIX", "CODESCAN_ISSUE_VIEW_DETAILS"]}, "CodeScanSucceededEvent": {"type": "structure", "required": ["programmingLanguage", "codeScanJobId", "timestamp", "numberOfFindings"], "members": {"programmingLanguage": {"shape": "ProgrammingLanguage"}, "codeScanJobId": {"shape": "CodeScanJobId"}, "timestamp": {"shape": "Timestamp"}, "numberOfFindings": {"shape": "PrimitiveInteger"}, "codeAnalysisScope": {"shape": "CodeAnalysisScope"}}}, "Completion": {"type": "structure", "required": ["content"], "members": {"content": {"shape": "CompletionContentString"}, "references": {"shape": "References"}, "mostRelevantMissingImports": {"shape": "Imports"}}}, "CompletionContentString": {"type": "string", "max": 5120, "min": 1, "sensitive": true}, "CompletionType": {"type": "string", "enum": ["BLOCK", "LINE"]}, "Completions": {"type": "list", "member": {"shape": "Completion"}, "max": 10, "min": 0}, "ConflictException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "reason": {"shape": "ConflictExceptionReason"}}, "exception": true}, "ConflictExceptionReason": {"type": "string", "enum": ["CUSTOMER_KMS_KEY_INVALID_KEY_POLICY", "CUSTOMER_KMS_KEY_DISABLED", "MISMATCHED_KMS_KEY"]}, "ConsoleState": {"type": "structure", "members": {"region": {"shape": "String"}, "consoleUrl": {"shape": "SensitiveString"}, "serviceId": {"shape": "String"}, "serviceConsolePage": {"shape": "String"}, "serviceSubconsolePage": {"shape": "String"}, "taskName": {"shape": "SensitiveString"}}}, "ContentChecksumType": {"type": "string", "enum": ["SHA_256"]}, "ContextTruncationScheme": {"type": "string", "enum": ["ANALYSIS", "GUMBY"]}, "ConversationId": {"type": "string", "max": 128, "min": 1}, "ConversationState": {"type": "structure", "required": ["currentMessage", "chatTriggerType"], "members": {"conversationId": {"shape": "ConversationId"}, "history": {"shape": "ChatHistory"}, "currentMessage": {"shape": "ChatMessage"}, "chatTriggerType": {"shape": "ChatTriggerType"}, "customizationArn": {"shape": "ResourceArn"}}}, "CreateTaskAssistConversationRequest": {"type": "structure", "members": {"profileArn": {"shape": "ProfileArn"}}}, "CreateTaskAssistConversationResponse": {"type": "structure", "required": ["conversationId"], "members": {"conversationId": {"shape": "ConversationId"}}}, "CreateUploadUrlRequest": {"type": "structure", "members": {"contentMd5": {"shape": "CreateUploadUrlRequestContentMd5String"}, "contentChecksum": {"shape": "CreateUploadUrlRequestContentChecksumString"}, "contentChecksumType": {"shape": "ContentChecksumType"}, "contentLength": {"shape": "CreateUploadUrlRequestContentLengthLong"}, "artifactType": {"shape": "ArtifactType"}, "uploadIntent": {"shape": "UploadIntent"}, "uploadContext": {"shape": "UploadContext"}, "uploadId": {"shape": "UploadId"}, "profileArn": {"shape": "ProfileArn"}}}, "CreateUploadUrlRequestContentChecksumString": {"type": "string", "max": 512, "min": 1, "sensitive": true}, "CreateUploadUrlRequestContentLengthLong": {"type": "long", "box": true, "min": 1}, "CreateUploadUrlRequestContentMd5String": {"type": "string", "max": 128, "min": 1, "sensitive": true}, "CreateUploadUrlResponse": {"type": "structure", "required": ["uploadId", "uploadUrl"], "members": {"uploadId": {"shape": "UploadId"}, "uploadUrl": {"shape": "PreSignedUrl"}, "kmsKeyArn": {"shape": "ResourceArn"}, "requestHeaders": {"shape": "RequestHeaders"}}}, "CreateWorkspaceRequest": {"type": "structure", "required": ["workspaceRoot"], "members": {"workspaceRoot": {"shape": "CreateWorkspaceRequestWorkspaceRootString"}, "profileArn": {"shape": "ProfileArn"}}}, "CreateWorkspaceRequestWorkspaceRootString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "CreateWorkspaceResponse": {"type": "structure", "required": ["workspace"], "members": {"workspace": {"shape": "WorkspaceMetadata"}}}, "CursorState": {"type": "structure", "members": {"position": {"shape": "Position"}, "range": {"shape": "Range"}}, "union": true}, "Customization": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "CustomizationArn"}, "name": {"shape": "CustomizationName"}, "description": {"shape": "Description"}}}, "CustomizationArn": {"type": "string", "max": 950, "min": 0, "pattern": "arn:[-.a-z0-9]{1,63}:codewhisperer:([-.a-z0-9]{0,63}:){2}([a-zA-Z0-9-_:/]){1,1023}"}, "CustomizationName": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z][a-zA-Z0-9_-]*"}, "Customizations": {"type": "list", "member": {"shape": "Customization"}}, "DashboardAnalytics": {"type": "structure", "required": ["toggle"], "members": {"toggle": {"shape": "OptInFeatureToggle"}}}, "DeleteTaskAssistConversationRequest": {"type": "structure", "required": ["conversationId"], "members": {"conversationId": {"shape": "ConversationId"}, "profileArn": {"shape": "ProfileArn"}}}, "DeleteTaskAssistConversationResponse": {"type": "structure", "required": ["conversationId"], "members": {"conversationId": {"shape": "ConversationId"}}}, "DeleteWorkspaceRequest": {"type": "structure", "required": ["workspaceId"], "members": {"workspaceId": {"shape": "UUID"}, "profileArn": {"shape": "ProfileArn"}}}, "DeleteWorkspaceResponse": {"type": "structure", "members": {}}, "Description": {"type": "string", "max": 256, "min": 0, "pattern": "[\\sa-zA-Z0-9_-]*"}, "Diagnostic": {"type": "structure", "members": {"textDocumentDiagnostic": {"shape": "TextDocumentDiagnostic"}, "runtimeDiagnostic": {"shape": "RuntimeDiagnostic"}}, "union": true}, "DiagnosticSeverity": {"type": "string", "enum": ["ERROR", "WARNING", "INFORMATION", "HINT"]}, "Dimension": {"type": "structure", "members": {"name": {"shape": "DimensionNameString"}, "value": {"shape": "DimensionValueString"}}}, "DimensionList": {"type": "list", "member": {"shape": "Dimension"}, "max": 30, "min": 0}, "DimensionNameString": {"type": "string", "max": 255, "min": 1, "pattern": "[-a-zA-Z0-9._]*"}, "DimensionValueString": {"type": "string", "max": 1024, "min": 1, "pattern": "[-a-zA-Z0-9._]*"}, "DocFolderLevel": {"type": "string", "enum": ["SUB_FOLDER", "ENTIRE_WORKSPACE"]}, "DocGenerationEvent": {"type": "structure", "required": ["conversationId"], "members": {"conversationId": {"shape": "ConversationId"}, "numberOfAddChars": {"shape": "PrimitiveInteger"}, "numberOfAddLines": {"shape": "PrimitiveInteger"}, "numberOfAddFiles": {"shape": "PrimitiveInteger"}, "userDecision": {"shape": "DocUserDecision"}, "interactionType": {"shape": "DocInteractionType"}, "userIdentity": {"shape": "String"}, "numberOfNavigation": {"shape": "PrimitiveInteger"}, "folderLevel": {"shape": "DocFolderLevel"}}}, "DocInteractionType": {"type": "string", "enum": ["GENERATE_README", "UPDATE_README", "EDIT_README"]}, "DocUserDecision": {"type": "string", "enum": ["ACCEPT", "REJECT"]}, "DocV2AcceptanceEvent": {"type": "structure", "required": ["conversationId", "numberOfAddedChars", "numberOfAddedLines", "numberOfAddedFiles", "userDecision", "interactionType", "numberOfNavigations", "folderLevel"], "members": {"conversationId": {"shape": "ConversationId"}, "numberOfAddedChars": {"shape": "DocV2AcceptanceEventNumberOfAddedCharsInteger"}, "numberOfAddedLines": {"shape": "DocV2AcceptanceEventNumberOfAddedLinesInteger"}, "numberOfAddedFiles": {"shape": "DocV2AcceptanceEventNumberOfAddedFilesInteger"}, "userDecision": {"shape": "DocUserDecision"}, "interactionType": {"shape": "DocInteractionType"}, "numberOfNavigations": {"shape": "DocV2AcceptanceEventNumberOfNavigationsInteger"}, "folderLevel": {"shape": "DocFolderLevel"}}}, "DocV2AcceptanceEventNumberOfAddedCharsInteger": {"type": "integer", "min": 0}, "DocV2AcceptanceEventNumberOfAddedFilesInteger": {"type": "integer", "min": 0}, "DocV2AcceptanceEventNumberOfAddedLinesInteger": {"type": "integer", "min": 0}, "DocV2AcceptanceEventNumberOfNavigationsInteger": {"type": "integer", "min": 0}, "DocV2GenerationEvent": {"type": "structure", "required": ["conversationId", "numberOfGeneratedChars", "numberOfGeneratedLines", "numberOfGeneratedFiles"], "members": {"conversationId": {"shape": "ConversationId"}, "numberOfGeneratedChars": {"shape": "DocV2GenerationEventNumberOfGeneratedCharsInteger"}, "numberOfGeneratedLines": {"shape": "DocV2GenerationEventNumberOfGeneratedLinesInteger"}, "numberOfGeneratedFiles": {"shape": "DocV2GenerationEventNumberOfGeneratedFilesInteger"}, "interactionType": {"shape": "DocInteractionType"}, "numberOfNavigations": {"shape": "DocV2GenerationEventNumberOfNavigationsInteger"}, "folderLevel": {"shape": "DocFolderLevel"}}}, "DocV2GenerationEventNumberOfGeneratedCharsInteger": {"type": "integer", "min": 0}, "DocV2GenerationEventNumberOfGeneratedFilesInteger": {"type": "integer", "min": 0}, "DocV2GenerationEventNumberOfGeneratedLinesInteger": {"type": "integer", "min": 0}, "DocV2GenerationEventNumberOfNavigationsInteger": {"type": "integer", "min": 0}, "DocumentSymbol": {"type": "structure", "required": ["name", "type"], "members": {"name": {"shape": "DocumentSymbolNameString"}, "type": {"shape": "SymbolType"}, "source": {"shape": "DocumentSymbolSourceString"}}}, "DocumentSymbolNameString": {"type": "string", "max": 256, "min": 1}, "DocumentSymbolSourceString": {"type": "string", "max": 256, "min": 1}, "DocumentSymbols": {"type": "list", "member": {"shape": "DocumentSymbol"}, "max": 1000, "min": 0}, "DocumentationIntentContext": {"type": "structure", "required": ["type"], "members": {"scope": {"shape": "DocumentationIntentContextScopeString"}, "type": {"shape": "DocumentationType"}}}, "DocumentationIntentContextScopeString": {"type": "string", "max": 4096, "min": 1, "sensitive": true}, "DocumentationType": {"type": "string", "enum": ["README"]}, "Double": {"type": "double", "box": true}, "EditorState": {"type": "structure", "members": {"document": {"shape": "TextDocument"}, "cursorState": {"shape": "CursorState"}, "relevantDocuments": {"shape": "RelevantDocumentList"}, "useRelevantDocuments": {"shape": "Boolean"}, "workspaceFolders": {"shape": "WorkspaceFolderList"}}}, "EnvState": {"type": "structure", "members": {"operatingSystem": {"shape": "EnvStateOperatingSystemString"}, "currentWorkingDirectory": {"shape": "EnvStateCurrentWorkingDirectoryString"}, "environmentVariables": {"shape": "EnvironmentVariables"}, "timezoneOffset": {"shape": "EnvStateTimezoneOffsetInteger"}}}, "EnvStateCurrentWorkingDirectoryString": {"type": "string", "max": 256, "min": 1, "sensitive": true}, "EnvStateOperatingSystemString": {"type": "string", "max": 32, "min": 1, "pattern": "(macos|linux|windows)"}, "EnvStateTimezoneOffsetInteger": {"type": "integer", "box": true, "max": 1440, "min": -1440}, "EnvironmentVariable": {"type": "structure", "members": {"key": {"shape": "EnvironmentVariableKeyString"}, "value": {"shape": "EnvironmentVariableValueString"}}}, "EnvironmentVariableKeyString": {"type": "string", "max": 256, "min": 1, "sensitive": true}, "EnvironmentVariableValueString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "EnvironmentVariables": {"type": "list", "member": {"shape": "EnvironmentVariable"}, "max": 100, "min": 0}, "ErrorDetails": {"type": "string", "max": 2048, "min": 0}, "Event": {"type": "structure", "required": ["eventId", "generationId", "eventTimestamp", "eventType", "eventBlob"], "members": {"eventId": {"shape": "UUID"}, "generationId": {"shape": "UUID"}, "eventTimestamp": {"shape": "SyntheticTimestamp_date_time"}, "eventType": {"shape": "EventType"}, "eventBlob": {"shape": "EventBlob"}}}, "EventBlob": {"type": "blob", "max": 400000, "min": 1, "sensitive": true}, "EventList": {"type": "list", "member": {"shape": "Event"}, "max": 10, "min": 1}, "EventType": {"type": "string", "max": 100, "min": 1}, "ExternalIdentityDetails": {"type": "structure", "members": {"issuerUrl": {"shape": "IssuerUrl"}, "clientId": {"shape": "ClientId"}, "scimEndpoint": {"shape": "String"}}}, "FeatureDevCodeAcceptanceEvent": {"type": "structure", "required": ["conversationId", "linesOfCodeAccepted", "charactersOfCodeAccepted"], "members": {"conversationId": {"shape": "ConversationId"}, "linesOfCodeAccepted": {"shape": "FeatureDevCodeAcceptanceEventLinesOfCodeAcceptedInteger"}, "charactersOfCodeAccepted": {"shape": "FeatureDevCodeAcceptanceEventCharactersOfCodeAcceptedInteger"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}}}, "FeatureDevCodeAcceptanceEventCharactersOfCodeAcceptedInteger": {"type": "integer", "min": 0}, "FeatureDevCodeAcceptanceEventLinesOfCodeAcceptedInteger": {"type": "integer", "min": 0}, "FeatureDevCodeGenerationEvent": {"type": "structure", "required": ["conversationId", "linesOfCodeGenerated", "charactersOfCodeGenerated"], "members": {"conversationId": {"shape": "ConversationId"}, "linesOfCodeGenerated": {"shape": "FeatureDevCodeGenerationEventLinesOfCodeGeneratedInteger"}, "charactersOfCodeGenerated": {"shape": "FeatureDevCodeGenerationEventCharactersOfCodeGeneratedInteger"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}}}, "FeatureDevCodeGenerationEventCharactersOfCodeGeneratedInteger": {"type": "integer", "min": 0}, "FeatureDevCodeGenerationEventLinesOfCodeGeneratedInteger": {"type": "integer", "min": 0}, "FeatureDevEvent": {"type": "structure", "required": ["conversationId"], "members": {"conversationId": {"shape": "ConversationId"}}}, "FeatureEvaluation": {"type": "structure", "required": ["feature", "variation", "value"], "members": {"feature": {"shape": "FeatureName"}, "variation": {"shape": "FeatureVariation"}, "value": {"shape": "FeatureValue"}}}, "FeatureEvaluationsList": {"type": "list", "member": {"shape": "FeatureEvaluation"}, "max": 50, "min": 0}, "FeatureName": {"type": "string", "max": 128, "min": 1, "pattern": "[-a-zA-Z0-9._]*"}, "FeatureValue": {"type": "structure", "members": {"boolValue": {"shape": "Boolean"}, "doubleValue": {"shape": "Double"}, "longValue": {"shape": "<PERSON>"}, "stringValue": {"shape": "FeatureValueStringType"}}, "union": true}, "FeatureValueStringType": {"type": "string", "max": 512, "min": 0}, "FeatureVariation": {"type": "string", "max": 128, "min": 1, "pattern": "[-a-zA-Z0-9._]*"}, "FileContext": {"type": "structure", "required": ["leftFileContent", "right<PERSON>ile<PERSON><PERSON>nt", "filename", "programmingLanguage"], "members": {"leftFileContent": {"shape": "FileContextLeftFileContentString"}, "rightFileContent": {"shape": "FileContextRightFileContentString"}, "filename": {"shape": "FileContextFilenameString"}, "fileUri": {"shape": "FileContextFileUriString"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}}}, "FileContextFileUriString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "FileContextFilenameString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "FileContextLeftFileContentString": {"type": "string", "max": 10240, "min": 0, "sensitive": true}, "FileContextRightFileContentString": {"type": "string", "max": 10240, "min": 0, "sensitive": true}, "FollowupPrompt": {"type": "structure", "required": ["content"], "members": {"content": {"shape": "FollowupPromptContentString"}, "userIntent": {"shape": "UserIntent"}}}, "FollowupPromptContentString": {"type": "string", "max": 4096, "min": 0, "sensitive": true}, "FunctionalityName": {"type": "string", "enum": ["COMPLETIONS", "ANALYSIS", "CONVERSATIONS", "TASK_ASSIST", "TRANSFORMATIONS", "CHAT_CUSTOMIZATION", "TRANSFORMATIONS_WEBAPP", "FEATURE_DEVELOPMENT"], "max": 64, "min": 1}, "GenerateCompletionsRequest": {"type": "structure", "required": ["fileContext"], "members": {"fileContext": {"shape": "FileContext"}, "maxResults": {"shape": "GenerateCompletionsRequestMaxResultsInteger"}, "nextToken": {"shape": "GenerateCompletionsRequestNextTokenString"}, "referenceTrackerConfiguration": {"shape": "ReferenceTrackerConfiguration"}, "supplementalContexts": {"shape": "SupplementalContextList"}, "customizationArn": {"shape": "CustomizationArn"}, "optOutPreference": {"shape": "OptOutPreference"}, "userContext": {"shape": "UserContext"}, "profileArn": {"shape": "ProfileArn"}, "workspaceId": {"shape": "UUID"}}}, "GenerateCompletionsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 10, "min": 1}, "GenerateCompletionsRequestNextTokenString": {"type": "string", "max": 2048, "min": 0, "pattern": "(?:[A-Za-z0-9\\+/]{4})*(?:[A-Za-z0-9\\+/]{2}\\=\\=|[A-Za-z0-9\\+/]{3}\\=)?", "sensitive": true}, "GenerateCompletionsResponse": {"type": "structure", "members": {"completions": {"shape": "Completions"}, "nextToken": {"shape": "SensitiveString"}}}, "GetCodeAnalysisRequest": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "GetCodeAnalysisRequestJobIdString"}, "profileArn": {"shape": "ProfileArn"}}}, "GetCodeAnalysisRequestJobIdString": {"type": "string", "max": 256, "min": 1}, "GetCodeAnalysisResponse": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "CodeAnalysisStatus"}, "errorMessage": {"shape": "SensitiveString"}}}, "GetCodeFixJobRequest": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "GetCodeFixJobRequestJobIdString"}, "profileArn": {"shape": "ProfileArn"}}}, "GetCodeFixJobRequestJobIdString": {"type": "string", "max": 256, "min": 1, "pattern": ".*[A-Za-z0-9-:]+.*"}, "GetCodeFixJobResponse": {"type": "structure", "members": {"jobStatus": {"shape": "CodeFixJobStatus"}, "suggestedFix": {"shape": "SuggestedFix"}}}, "GetTaskAssistCodeGenerationRequest": {"type": "structure", "required": ["conversationId", "codeGenerationId"], "members": {"conversationId": {"shape": "ConversationId"}, "codeGenerationId": {"shape": "CodeGenerationId"}, "profileArn": {"shape": "ProfileArn"}}}, "GetTaskAssistCodeGenerationResponse": {"type": "structure", "required": ["conversationId", "codeGenerationStatus"], "members": {"conversationId": {"shape": "ConversationId"}, "codeGenerationStatus": {"shape": "CodeGenerationStatus"}, "codeGenerationStatusDetail": {"shape": "CodeGenerationStatusDetail"}, "codeGenerationRemainingIterationCount": {"shape": "Integer"}, "codeGenerationTotalIterationCount": {"shape": "Integer"}}}, "GetTestGenerationRequest": {"type": "structure", "required": ["testGenerationJobGroupName", "testGenerationJobId"], "members": {"testGenerationJobGroupName": {"shape": "TestGenerationJobGroupName"}, "testGenerationJobId": {"shape": "UUID"}, "profileArn": {"shape": "ProfileArn"}}}, "GetTestGenerationResponse": {"type": "structure", "members": {"testGenerationJob": {"shape": "TestGenerationJob"}}}, "GetTransformationPlanRequest": {"type": "structure", "required": ["transformationJobId"], "members": {"transformationJobId": {"shape": "TransformationJobId"}, "profileArn": {"shape": "ProfileArn"}}}, "GetTransformationPlanResponse": {"type": "structure", "required": ["transformationPlan"], "members": {"transformationPlan": {"shape": "TransformationPlan"}}}, "GetTransformationRequest": {"type": "structure", "required": ["transformationJobId"], "members": {"transformationJobId": {"shape": "TransformationJobId"}, "profileArn": {"shape": "ProfileArn"}}}, "GetTransformationResponse": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"transformationJob": {"shape": "TransformationJob"}}}, "GitState": {"type": "structure", "members": {"status": {"shape": "GitStateStatusString"}}}, "GitStateStatusString": {"type": "string", "max": 4096, "min": 0, "sensitive": true}, "IdeCategory": {"type": "string", "enum": ["JETBRAINS", "VSCODE", "CLI", "JUPYTER_MD", "JUPYTER_SM", "ECLIPSE", "VISUAL_STUDIO"], "max": 64, "min": 1}, "IdeDiagnostic": {"type": "structure", "required": ["ideDiagnosticType"], "members": {"range": {"shape": "Range"}, "source": {"shape": "IdeDiagnosticSourceString"}, "severity": {"shape": "DiagnosticSeverity"}, "ideDiagnosticType": {"shape": "IdeDiagnosticType"}}}, "IdeDiagnosticList": {"type": "list", "member": {"shape": "IdeDiagnostic"}, "max": 1024, "min": 0}, "IdeDiagnosticSourceString": {"type": "string", "max": 1024, "min": 0, "sensitive": true}, "IdeDiagnosticType": {"type": "string", "enum": ["SYNTAX_ERROR", "TYPE_ERROR", "REFERENCE_ERROR", "BEST_PRACTICE", "SECURITY", "OTHER"]}, "IdempotencyToken": {"type": "string", "max": 256, "min": 1}, "IdentityDetails": {"type": "structure", "members": {"ssoIdentityDetails": {"shape": "SSOIdentityDetails"}, "externalIdentityDetails": {"shape": "ExternalIdentityDetails"}}, "union": true}, "ImageBlock": {"type": "structure", "required": ["format", "source"], "members": {"format": {"shape": "ImageFormat"}, "source": {"shape": "ImageSource"}}}, "ImageBlocks": {"type": "list", "member": {"shape": "ImageBlock"}, "max": 10, "min": 0}, "ImageFormat": {"type": "string", "enum": ["png", "jpeg", "gif", "webp"]}, "ImageSource": {"type": "structure", "members": {"bytes": {"shape": "ImageSourceBytesBlob"}}, "sensitive": true, "union": true}, "ImageSourceBytesBlob": {"type": "blob", "max": 1500000, "min": 1}, "Import": {"type": "structure", "members": {"statement": {"shape": "ImportStatementString"}}}, "ImportStatementString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "Imports": {"type": "list", "member": {"shape": "Import"}, "max": 10, "min": 0}, "InlineChatEvent": {"type": "structure", "required": ["requestId", "timestamp"], "members": {"requestId": {"shape": "UUID"}, "timestamp": {"shape": "Timestamp"}, "inputLength": {"shape": "PrimitiveInteger"}, "numSelectedLines": {"shape": "PrimitiveInteger"}, "numSuggestionAddChars": {"shape": "PrimitiveInteger"}, "numSuggestionAddLines": {"shape": "PrimitiveInteger"}, "numSuggestionDelChars": {"shape": "PrimitiveInteger"}, "numSuggestionDelLines": {"shape": "PrimitiveInteger"}, "codeIntent": {"shape": "Boolean"}, "userDecision": {"shape": "InlineChatUserDecision"}, "responseStartLatency": {"shape": "Double"}, "responseEndLatency": {"shape": "Double"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}}}, "InlineChatUserDecision": {"type": "string", "enum": ["ACCEPT", "REJECT", "DISMISS"]}, "Integer": {"type": "integer", "box": true}, "Intent": {"type": "string", "enum": ["DEV", "DOC"]}, "IntentContext": {"type": "structure", "members": {"documentation": {"shape": "DocumentationIntentContext"}}, "union": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "IssuerUrl": {"type": "string", "max": 255, "min": 1}, "LineRangeList": {"type": "list", "member": {"shape": "Range"}}, "ListAvailableCustomizationsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListAvailableCustomizationsRequestMaxResultsInteger"}, "nextToken": {"shape": "Base64EncodedPaginationToken"}, "profileArn": {"shape": "ProfileArn"}}}, "ListAvailableCustomizationsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListAvailableCustomizationsResponse": {"type": "structure", "required": ["customizations"], "members": {"customizations": {"shape": "Customizations"}, "nextToken": {"shape": "Base64EncodedPaginationToken"}}}, "ListAvailableProfilesRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListAvailableProfilesRequestMaxResultsInteger"}, "nextToken": {"shape": "Base64EncodedPaginationToken"}}}, "ListAvailableProfilesRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 10, "min": 1}, "ListAvailableProfilesResponse": {"type": "structure", "required": ["profiles"], "members": {"profiles": {"shape": "ProfileList"}, "nextToken": {"shape": "Base64EncodedPaginationToken"}}}, "ListCodeAnalysisFindingsRequest": {"type": "structure", "required": ["jobId", "codeAnalysisFindingsSchema"], "members": {"jobId": {"shape": "ListCodeAnalysisFindingsRequestJobIdString"}, "nextToken": {"shape": "PaginationToken"}, "codeAnalysisFindingsSchema": {"shape": "CodeAnalysisFindingsSchema"}, "profileArn": {"shape": "ProfileArn"}}}, "ListCodeAnalysisFindingsRequestJobIdString": {"type": "string", "max": 256, "min": 1}, "ListCodeAnalysisFindingsResponse": {"type": "structure", "required": ["codeAnalysisFindings"], "members": {"nextToken": {"shape": "PaginationToken"}, "codeAnalysisFindings": {"shape": "SensitiveString"}}}, "ListEventsRequest": {"type": "structure", "required": ["conversationId"], "members": {"conversationId": {"shape": "UUID"}, "maxResults": {"shape": "ListEventsRequestMaxResultsInteger"}, "nextToken": {"shape": "NextToken"}}}, "ListEventsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 50, "min": 1}, "ListEventsResponse": {"type": "structure", "required": ["conversationId", "events"], "members": {"conversationId": {"shape": "UUID"}, "events": {"shape": "EventList"}, "nextToken": {"shape": "NextToken"}}}, "ListFeatureEvaluationsRequest": {"type": "structure", "required": ["userContext"], "members": {"userContext": {"shape": "UserContext"}, "profileArn": {"shape": "ProfileArn"}}}, "ListFeatureEvaluationsResponse": {"type": "structure", "required": ["featureEvaluations"], "members": {"featureEvaluations": {"shape": "FeatureEvaluationsList"}}}, "ListWorkspaceMetadataRequest": {"type": "structure", "required": ["workspaceRoot"], "members": {"workspaceRoot": {"shape": "ListWorkspaceMetadataRequestWorkspaceRootString"}, "nextToken": {"shape": "String"}, "maxResults": {"shape": "Integer"}, "profileArn": {"shape": "ProfileArn"}}}, "ListWorkspaceMetadataRequestWorkspaceRootString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "ListWorkspaceMetadataResponse": {"type": "structure", "required": ["workspaces"], "members": {"workspaces": {"shape": "WorkspaceList"}, "nextToken": {"shape": "String"}}}, "Long": {"type": "long", "box": true}, "MessageId": {"type": "string", "max": 128, "min": 0}, "MetricData": {"type": "structure", "required": ["metricName", "metricValue", "timestamp", "product"], "members": {"metricName": {"shape": "MetricDataMetricNameString"}, "metricValue": {"shape": "Double"}, "timestamp": {"shape": "Timestamp"}, "product": {"shape": "MetricDataProductString"}, "dimensions": {"shape": "DimensionList"}}}, "MetricDataMetricNameString": {"type": "string", "max": 1024, "min": 1, "pattern": "[-a-zA-Z0-9._]*"}, "MetricDataProductString": {"type": "string", "max": 128, "min": 1, "pattern": "[-a-zA-Z0-9._]*"}, "NextToken": {"type": "string", "max": 1000, "min": 0}, "Notifications": {"type": "list", "member": {"shape": "NotificationsFeature"}, "max": 10, "min": 0}, "NotificationsFeature": {"type": "structure", "required": ["feature", "toggle"], "members": {"feature": {"shape": "FeatureName"}, "toggle": {"shape": "OptInFeatureToggle"}}}, "OperatingSystem": {"type": "string", "enum": ["MAC", "WINDOWS", "LINUX"], "max": 64, "min": 1}, "OptInFeatureToggle": {"type": "string", "enum": ["ON", "OFF"]}, "OptInFeatures": {"type": "structure", "members": {"promptLogging": {"shape": "PromptLogging"}, "byUserAnalytics": {"shape": "ByUserAnalytics"}, "dashboardAnalytics": {"shape": "DashboardAnalytics"}, "notifications": {"shape": "Notifications"}, "workspaceContext": {"shape": "WorkspaceContext"}}}, "OptOutPreference": {"type": "string", "enum": ["OPTIN", "OPTOUT"]}, "Origin": {"type": "string", "enum": ["CHATBOT", "CONSOLE", "DOCUMENTATION", "MARKETING", "MOBILE", "SERVICE_INTERNAL", "UNIFIED_SEARCH", "UNKNOWN", "MD", "IDE", "SAGE_MAKER", "CLI", "AI_EDITOR", "OPENSEARCH_DASHBOARD", "GITLAB"]}, "PackageInfo": {"type": "structure", "members": {"executionCommand": {"shape": "SensitiveString"}, "buildCommand": {"shape": "SensitiveString"}, "buildOrder": {"shape": "PackageInfoBuildOrderInteger"}, "testFramework": {"shape": "String"}, "packageSummary": {"shape": "PackageInfoPackageSummaryString"}, "packagePlan": {"shape": "PackageInfoPackagePlanString"}, "targetFileInfoList": {"shape": "TargetFileInfoList"}}}, "PackageInfoBuildOrderInteger": {"type": "integer", "box": true, "min": 0}, "PackageInfoList": {"type": "list", "member": {"shape": "PackageInfo"}}, "PackageInfoPackagePlanString": {"type": "string", "max": 30720, "min": 0, "sensitive": true}, "PackageInfoPackageSummaryString": {"type": "string", "max": 30720, "min": 0, "sensitive": true}, "PaginationToken": {"type": "string", "max": 2048, "min": 1, "pattern": "\\S+"}, "Position": {"type": "structure", "required": ["line", "character"], "members": {"line": {"shape": "Integer"}, "character": {"shape": "Integer"}}}, "PreSignedUrl": {"type": "string", "max": 2048, "min": 1, "sensitive": true}, "PrimitiveInteger": {"type": "integer"}, "Profile": {"type": "structure", "required": ["arn", "profileName"], "members": {"arn": {"shape": "ProfileArn"}, "identityDetails": {"shape": "IdentityDetails"}, "profileName": {"shape": "ProfileName"}, "description": {"shape": "ProfileDescription"}, "referenceTrackerConfiguration": {"shape": "ReferenceTrackerConfiguration"}, "kmsKeyArn": {"shape": "ResourceArn"}, "activeFunctionalities": {"shape": "ActiveFunctionalityList"}, "status": {"shape": "ProfileStatus"}, "errorDetails": {"shape": "ErrorDetails"}, "resourcePolicy": {"shape": "ResourcePolicy"}, "profileType": {"shape": "ProfileType"}, "optInFeatures": {"shape": "OptInFeatures"}, "permissionUpdateRequired": {"shape": "Boolean"}, "applicationProperties": {"shape": "ApplicationPropertiesList"}}}, "ProfileArn": {"type": "string", "max": 950, "min": 0, "pattern": "arn:aws:codewhisperer:[-.a-z0-9]{1,63}:\\d{12}:profile/([a-zA-Z0-9]){12}"}, "ProfileDescription": {"type": "string", "max": 256, "min": 1, "pattern": "[\\sa-zA-Z0-9_-]*"}, "ProfileList": {"type": "list", "member": {"shape": "Profile"}}, "ProfileName": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z][a-zA-Z0-9_-]*"}, "ProfileStatus": {"type": "string", "enum": ["ACTIVE", "CREATING", "CREATE_FAILED", "UPDATING", "UPDATE_FAILED", "DELETING", "DELETE_FAILED"]}, "ProfileType": {"type": "string", "enum": ["Q_DEVELOPER", "CODEWHISPERER"]}, "ProgrammingLanguage": {"type": "structure", "required": ["languageName"], "members": {"languageName": {"shape": "ProgrammingLanguageLanguageNameString"}}}, "ProgrammingLanguageLanguageNameString": {"type": "string", "max": 128, "min": 1, "pattern": "(python|javascript|java|csharp|typescript|c|cpp|go|kotlin|php|ruby|rust|scala|shell|sql|json|yaml|vue|tf|tsx|jsx|plaintext|systemverilog|dart|lua|swift|powershell|r)"}, "ProgressUpdates": {"type": "list", "member": {"shape": "TransformationProgressUpdate"}}, "PromptLogging": {"type": "structure", "required": ["s3Uri", "toggle"], "members": {"s3Uri": {"shape": "S3Uri"}, "toggle": {"shape": "OptInFeatureToggle"}}}, "Range": {"type": "structure", "required": ["start", "end"], "members": {"start": {"shape": "Position"}, "end": {"shape": "Position"}}}, "RecommendationsWithReferencesPreference": {"type": "string", "enum": ["BLOCK", "ALLOW"]}, "Reference": {"type": "structure", "members": {"licenseName": {"shape": "ReferenceLicenseNameString"}, "repository": {"shape": "ReferenceRepositoryString"}, "url": {"shape": "ReferenceUrlString"}, "recommendationContentSpan": {"shape": "Span"}}}, "ReferenceLicenseNameString": {"type": "string", "max": 1024, "min": 1}, "ReferenceRepositoryString": {"type": "string", "max": 1024, "min": 1}, "ReferenceTrackerConfiguration": {"type": "structure", "required": ["recommendationsWithReferences"], "members": {"recommendationsWithReferences": {"shape": "RecommendationsWithReferencesPreference"}}}, "ReferenceUrlString": {"type": "string", "max": 1024, "min": 1}, "References": {"type": "list", "member": {"shape": "Reference"}, "max": 10, "min": 0}, "RelevantDocumentList": {"type": "list", "member": {"shape": "RelevantTextDocument"}, "max": 30, "min": 0}, "RelevantTextDocument": {"type": "structure", "required": ["relativeFilePath"], "members": {"relativeFilePath": {"shape": "RelevantTextDocumentRelativeFilePathString"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "text": {"shape": "RelevantTextDocumentTextString"}, "documentSymbols": {"shape": "DocumentSymbols"}}}, "RelevantTextDocumentRelativeFilePathString": {"type": "string", "max": 4096, "min": 1, "sensitive": true}, "RelevantTextDocumentTextString": {"type": "string", "max": 40960, "min": 0, "sensitive": true}, "RequestHeaderKey": {"type": "string", "max": 64, "min": 1}, "RequestHeaderValue": {"type": "string", "max": 256, "min": 1}, "RequestHeaders": {"type": "map", "key": {"shape": "RequestHeaderKey"}, "value": {"shape": "RequestHeaderValue"}, "max": 16, "min": 1, "sensitive": true}, "ResourceArn": {"type": "string", "max": 1224, "min": 0, "pattern": "arn:([-.a-z0-9]{1,63}:){2}([-.a-z0-9]{0,63}:){2}([a-zA-Z0-9-_:/]){1,1023}"}, "ResourceNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "exception": true}, "ResourcePolicy": {"type": "structure", "required": ["effect"], "members": {"effect": {"shape": "ResourcePolicyEffect"}}}, "ResourcePolicyEffect": {"type": "string", "enum": ["ALLOW", "DENY"]}, "ResumeTransformationRequest": {"type": "structure", "required": ["transformationJobId"], "members": {"transformationJobId": {"shape": "TransformationJobId"}, "userActionStatus": {"shape": "TransformationUserActionStatus"}, "profileArn": {"shape": "ProfileArn"}}}, "ResumeTransformationResponse": {"type": "structure", "required": ["transformationStatus"], "members": {"transformationStatus": {"shape": "TransformationStatus"}}}, "RuntimeDiagnostic": {"type": "structure", "required": ["source", "severity", "message"], "members": {"source": {"shape": "RuntimeDiagnosticSourceString"}, "severity": {"shape": "DiagnosticSeverity"}, "message": {"shape": "RuntimeDiagnosticMessageString"}}}, "RuntimeDiagnosticMessageString": {"type": "string", "max": 1024, "min": 0, "sensitive": true}, "RuntimeDiagnosticSourceString": {"type": "string", "max": 1024, "min": 0, "sensitive": true}, "S3Uri": {"type": "string", "max": 1024, "min": 1, "pattern": "s3://((?!xn--)[a-z0-9](?![^/]*[.]{2})[a-z0-9-.]{1,61}[a-z0-9](?<!-s3alias)(?<!--ol-s3)(?<!([0-9]{1,3}\\.)([0-9]{1,3}\\.)([0-9]{1,3}\\.)([0-9]{1,3})))/(.{0,1024})"}, "SSOIdentityDetails": {"type": "structure", "required": ["instanceArn", "oidcClientId"], "members": {"instanceArn": {"shape": "ResourceArn"}, "oidcClientId": {"shape": "String"}, "ssoRegion": {"shape": "SSORegion"}}}, "SSORegion": {"type": "string", "max": 63, "min": 1, "pattern": "[-a-z0-9]{1,63}"}, "SendTelemetryEventRequest": {"type": "structure", "required": ["telemetryEvent"], "members": {"clientToken": {"shape": "IdempotencyToken", "idempotencyToken": true}, "telemetryEvent": {"shape": "TelemetryEvent"}, "optOutPreference": {"shape": "OptOutPreference"}, "userContext": {"shape": "UserContext"}, "profileArn": {"shape": "ProfileArn"}}}, "SendTelemetryEventResponse": {"type": "structure", "members": {}}, "SensitiveDocument": {"type": "structure", "members": {}, "document": true, "sensitive": true}, "SensitiveString": {"type": "string", "sensitive": true}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "exception": true}, "ShellHistory": {"type": "list", "member": {"shape": "ShellHistoryEntry"}, "max": 20, "min": 0}, "ShellHistoryEntry": {"type": "structure", "required": ["command"], "members": {"command": {"shape": "ShellHistoryEntryCommandString"}, "directory": {"shape": "ShellHistoryEntryDirectoryString"}, "exitCode": {"shape": "Integer"}, "stdout": {"shape": "ShellHistoryEntryStdoutString"}, "stderr": {"shape": "ShellHistoryEntryStderrString"}}}, "ShellHistoryEntryCommandString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "ShellHistoryEntryDirectoryString": {"type": "string", "max": 256, "min": 1, "sensitive": true}, "ShellHistoryEntryStderrString": {"type": "string", "max": 4096, "min": 0, "sensitive": true}, "ShellHistoryEntryStdoutString": {"type": "string", "max": 4096, "min": 0, "sensitive": true}, "ShellState": {"type": "structure", "required": ["shellName"], "members": {"shellName": {"shape": "ShellStateShellNameString"}, "shellHistory": {"shape": "ShellHistory"}}}, "ShellStateShellNameString": {"type": "string", "max": 32, "min": 1, "pattern": "(zsh|bash|fish|pwsh|nu)"}, "Span": {"type": "structure", "members": {"start": {"shape": "SpanStartInteger"}, "end": {"shape": "SpanEndInteger"}}}, "SpanEndInteger": {"type": "integer", "box": true, "min": 0}, "SpanStartInteger": {"type": "integer", "box": true, "min": 0}, "StartCodeAnalysisRequest": {"type": "structure", "required": ["artifacts", "programmingLanguage"], "members": {"artifacts": {"shape": "ArtifactMap"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "clientToken": {"shape": "StartCodeAnalysisRequestClientTokenString", "idempotencyToken": true}, "scope": {"shape": "CodeAnalysisScope"}, "codeScanName": {"shape": "CodeScanName"}, "profileArn": {"shape": "ProfileArn"}}}, "StartCodeAnalysisRequestClientTokenString": {"type": "string", "max": 256, "min": 1}, "StartCodeAnalysisResponse": {"type": "structure", "required": ["jobId", "status"], "members": {"jobId": {"shape": "StartCodeAnalysisResponseJobIdString"}, "status": {"shape": "CodeAnalysisStatus"}, "errorMessage": {"shape": "SensitiveString"}}}, "StartCodeAnalysisResponseJobIdString": {"type": "string", "max": 256, "min": 1}, "StartCodeFixJobRequest": {"type": "structure", "required": ["snippetRange", "uploadId"], "members": {"snippetRange": {"shape": "Range"}, "uploadId": {"shape": "UploadId"}, "description": {"shape": "StartCodeFixJobRequestDescriptionString"}, "ruleId": {"shape": "StartCodeFixJobRequestRuleIdString"}, "codeFixName": {"shape": "CodeFixName"}, "referenceTrackerConfiguration": {"shape": "ReferenceTrackerConfiguration"}, "profileArn": {"shape": "ProfileArn"}}}, "StartCodeFixJobRequestDescriptionString": {"type": "string", "max": 5000, "min": 1, "sensitive": true}, "StartCodeFixJobRequestRuleIdString": {"type": "string", "max": 256, "min": 1, "pattern": ".*[A-Za-z0-9-]+.*"}, "StartCodeFixJobResponse": {"type": "structure", "members": {"jobId": {"shape": "StartCodeFixJobResponseJobIdString"}, "status": {"shape": "CodeFixJobStatus"}}}, "StartCodeFixJobResponseJobIdString": {"type": "string", "max": 256, "min": 1, "pattern": ".*[A-Za-z0-9-:]+.*"}, "StartTaskAssistCodeGenerationRequest": {"type": "structure", "required": ["conversationState", "workspaceState"], "members": {"conversationState": {"shape": "ConversationState"}, "workspaceState": {"shape": "WorkspaceState"}, "taskAssistPlan": {"shape": "TaskAssistPlan"}, "codeGenerationId": {"shape": "CodeGenerationId"}, "currentCodeGenerationId": {"shape": "CodeGenerationId"}, "intent": {"shape": "Intent"}, "intentContext": {"shape": "IntentContext"}, "profileArn": {"shape": "ProfileArn"}}}, "StartTaskAssistCodeGenerationResponse": {"type": "structure", "required": ["conversationId", "codeGenerationId"], "members": {"conversationId": {"shape": "ConversationId"}, "codeGenerationId": {"shape": "CodeGenerationId"}}}, "StartTestGenerationRequest": {"type": "structure", "required": ["uploadId", "targetCodeList", "userInput"], "members": {"uploadId": {"shape": "UploadId"}, "targetCodeList": {"shape": "TargetCodeList"}, "userInput": {"shape": "StartTestGenerationRequestUserInputString"}, "testGenerationJobGroupName": {"shape": "TestGenerationJobGroupName"}, "clientToken": {"shape": "StartTestGenerationRequestClientTokenString", "idempotencyToken": true}, "profileArn": {"shape": "ProfileArn"}, "referenceTrackerConfiguration": {"shape": "ReferenceTrackerConfiguration"}}}, "StartTestGenerationRequestClientTokenString": {"type": "string", "max": 256, "min": 1}, "StartTestGenerationRequestUserInputString": {"type": "string", "max": 4096, "min": 0, "sensitive": true}, "StartTestGenerationResponse": {"type": "structure", "members": {"testGenerationJob": {"shape": "TestGenerationJob"}}}, "StartTransformationRequest": {"type": "structure", "required": ["workspaceState", "transformationSpec"], "members": {"workspaceState": {"shape": "WorkspaceState"}, "transformationSpec": {"shape": "TransformationSpec"}, "profileArn": {"shape": "ProfileArn"}}}, "StartTransformationResponse": {"type": "structure", "required": ["transformationJobId"], "members": {"transformationJobId": {"shape": "TransformationJobId"}}}, "StepId": {"type": "string", "max": 126, "min": 1}, "StopTransformationRequest": {"type": "structure", "required": ["transformationJobId"], "members": {"transformationJobId": {"shape": "TransformationJobId"}, "profileArn": {"shape": "ProfileArn"}}}, "StopTransformationResponse": {"type": "structure", "required": ["transformationStatus"], "members": {"transformationStatus": {"shape": "TransformationStatus"}}}, "String": {"type": "string"}, "SuggestedFix": {"type": "structure", "members": {"codeDiff": {"shape": "SuggestedFixCodeDiffString"}, "description": {"shape": "SuggestedFixDescriptionString"}, "references": {"shape": "References"}}}, "SuggestedFixCodeDiffString": {"type": "string", "max": 200000, "min": 0, "sensitive": true}, "SuggestedFixDescriptionString": {"type": "string", "max": 2000, "min": 1, "sensitive": true}, "SuggestionState": {"type": "string", "enum": ["ACCEPT", "REJECT", "DISCARD", "EMPTY", "MERGE"]}, "SupplementalContext": {"type": "structure", "required": ["filePath", "content"], "members": {"filePath": {"shape": "SupplementalContextFilePathString"}, "content": {"shape": "SupplementalContextContentString"}}}, "SupplementalContextContentString": {"type": "string", "max": 10240, "min": 1, "sensitive": true}, "SupplementalContextFilePathString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "SupplementalContextList": {"type": "list", "member": {"shape": "SupplementalContext"}, "max": 5, "min": 0}, "SupplementaryWebLink": {"type": "structure", "required": ["url", "title"], "members": {"url": {"shape": "SupplementaryWebLinkUrlString"}, "title": {"shape": "SupplementaryWebLinkTitleString"}, "snippet": {"shape": "SupplementaryWebLinkSnippetString"}}}, "SupplementaryWebLinkSnippetString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "SupplementaryWebLinkTitleString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "SupplementaryWebLinkUrlString": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "SupplementaryWebLinks": {"type": "list", "member": {"shape": "SupplementaryWebLink"}, "max": 10, "min": 0}, "SymbolType": {"type": "string", "enum": ["DECLARATION", "USAGE"]}, "SyntheticTimestamp_date_time": {"type": "timestamp", "timestampFormat": "iso8601"}, "TargetCode": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"relativeTargetPath": {"shape": "TargetCodeRelativeTargetPathString"}, "targetLineRangeList": {"shape": "LineRangeList"}}}, "TargetCodeList": {"type": "list", "member": {"shape": "TargetCode"}, "min": 1}, "TargetCodeRelativeTargetPathString": {"type": "string", "max": 4096, "min": 1, "sensitive": true}, "TargetFileInfo": {"type": "structure", "members": {"filePath": {"shape": "SensitiveString"}, "testFilePath": {"shape": "SensitiveString"}, "testCoverage": {"shape": "TargetFileInfoTestCoverageInteger"}, "fileSummary": {"shape": "TargetFileInfoFileSummaryString"}, "filePlan": {"shape": "TargetFileInfoFilePlanString"}, "codeReferences": {"shape": "References"}, "numberOfTestMethods": {"shape": "TargetFileInfoNumberOfTestMethodsInteger"}}}, "TargetFileInfoFilePlanString": {"type": "string", "max": 30720, "min": 0, "sensitive": true}, "TargetFileInfoFileSummaryString": {"type": "string", "max": 30720, "min": 0, "sensitive": true}, "TargetFileInfoList": {"type": "list", "member": {"shape": "TargetFileInfo"}}, "TargetFileInfoNumberOfTestMethodsInteger": {"type": "integer", "box": true, "min": 0}, "TargetFileInfoTestCoverageInteger": {"type": "integer", "box": true, "max": 100, "min": 0}, "TaskAssistPlan": {"type": "list", "member": {"shape": "TaskAssistPlanStep"}, "min": 0}, "TaskAssistPlanStep": {"type": "structure", "required": ["filePath", "description"], "members": {"filePath": {"shape": "TaskAssistPlanStepFilePathString"}, "description": {"shape": "TaskAssistPlanStepDescriptionString"}, "startLine": {"shape": "TaskAssistPlanStepStartLineInteger"}, "endLine": {"shape": "TaskAssistPlanStepEndLineInteger"}, "action": {"shape": "TaskAssistPlanStepAction"}}}, "TaskAssistPlanStepAction": {"type": "string", "enum": ["MODIFY", "CREATE", "DELETE", "UNKNOWN"]}, "TaskAssistPlanStepDescriptionString": {"type": "string", "max": 1024, "min": 1}, "TaskAssistPlanStepEndLineInteger": {"type": "integer", "box": true, "min": 0}, "TaskAssistPlanStepFilePathString": {"type": "string", "max": 1024, "min": 1}, "TaskAssistPlanStepStartLineInteger": {"type": "integer", "box": true, "min": 0}, "TaskAssistPlanningUploadContext": {"type": "structure", "required": ["conversationId"], "members": {"conversationId": {"shape": "ConversationId"}}}, "TelemetryEvent": {"type": "structure", "members": {"userTriggerDecisionEvent": {"shape": "UserTriggerDecisionEvent"}, "codeCoverageEvent": {"shape": "CodeCoverageEvent"}, "userModificationEvent": {"shape": "UserModificationEvent"}, "codeScanEvent": {"shape": "CodeScanEvent"}, "codeScanSucceededEvent": {"shape": "CodeScanSucceededEvent"}, "codeScanFailedEvent": {"shape": "CodeScanFailedEvent"}, "codeScanRemediationsEvent": {"shape": "CodeScanRemediationsEvent"}, "codeFixGenerationEvent": {"shape": "CodeFixGenerationEvent"}, "codeFixAcceptanceEvent": {"shape": "CodeFixAcceptanceEvent"}, "metricData": {"shape": "MetricData"}, "chatAddMessageEvent": {"shape": "ChatAddMessageEvent"}, "chatInteractWithMessageEvent": {"shape": "ChatInteractWithMessageEvent"}, "chatUserModificationEvent": {"shape": "ChatUserModificationEvent"}, "terminalUserInteractionEvent": {"shape": "TerminalUserInteractionEvent"}, "featureDevEvent": {"shape": "FeatureDevEvent"}, "featureDevCodeGenerationEvent": {"shape": "FeatureDevCodeGenerationEvent"}, "featureDevCodeAcceptanceEvent": {"shape": "FeatureDevCodeAcceptanceEvent"}, "inlineChatEvent": {"shape": "InlineChatEvent"}, "transformEvent": {"shape": "TransformEvent"}, "docGenerationEvent": {"shape": "DocGenerationEvent"}, "docV2GenerationEvent": {"shape": "DocV2GenerationEvent"}, "docV2AcceptanceEvent": {"shape": "DocV2AcceptanceEvent"}, "testGenerationEvent": {"shape": "TestGenerationEvent"}}, "union": true}, "TenantId": {"type": "string", "max": 1024, "min": 1}, "TerminalUserInteractionEvent": {"type": "structure", "members": {"terminalUserInteractionEventType": {"shape": "TerminalUserInteractionEventType"}, "terminal": {"shape": "String"}, "terminalVersion": {"shape": "String"}, "shell": {"shape": "String"}, "shellVersion": {"shape": "String"}, "duration": {"shape": "Integer"}, "timeToSuggestion": {"shape": "Integer"}, "isCompletionAccepted": {"shape": "Boolean"}, "cliToolCommand": {"shape": "String"}}}, "TerminalUserInteractionEventType": {"type": "string", "enum": ["CODEWHISPERER_TERMINAL_TRANSLATION_ACTION", "CODEWHISPERER_TERMINAL_COMPLETION_INSERTED"]}, "TestGenerationEvent": {"type": "structure", "required": ["jobId", "groupName"], "members": {"jobId": {"shape": "UUID"}, "groupName": {"shape": "TestGenerationJobGroupName"}, "timestamp": {"shape": "Timestamp"}, "ideCategory": {"shape": "IdeCategory"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "numberOfUnitTestCasesGenerated": {"shape": "Integer"}, "numberOfUnitTestCasesAccepted": {"shape": "Integer"}, "linesOfCodeGenerated": {"shape": "Integer"}, "linesOfCodeAccepted": {"shape": "Integer"}, "charsOfCodeGenerated": {"shape": "Integer"}, "charsOfCodeAccepted": {"shape": "Integer"}}}, "TestGenerationJob": {"type": "structure", "required": ["testGenerationJobId", "testGenerationJobGroupName", "status", "creationTime"], "members": {"testGenerationJobId": {"shape": "UUID"}, "testGenerationJobGroupName": {"shape": "TestGenerationJobGroupName"}, "status": {"shape": "TestGenerationJobStatus"}, "shortAnswer": {"shape": "SensitiveString"}, "creationTime": {"shape": "Timestamp"}, "progressRate": {"shape": "TestGenerationJobProgressRateInteger"}, "jobStatusReason": {"shape": "String"}, "jobSummary": {"shape": "TestGenerationJobJobSummaryString"}, "jobPlan": {"shape": "TestGenerationJobJobPlanString"}, "packageInfoList": {"shape": "PackageInfoList"}}}, "TestGenerationJobGroupName": {"type": "string", "max": 128, "min": 1, "pattern": "[a-zA-Z0-9-_]+"}, "TestGenerationJobJobPlanString": {"type": "string", "max": 30720, "min": 0, "sensitive": true}, "TestGenerationJobJobSummaryString": {"type": "string", "max": 30720, "min": 0, "sensitive": true}, "TestGenerationJobProgressRateInteger": {"type": "integer", "box": true, "max": 100, "min": 0}, "TestGenerationJobStatus": {"type": "string", "enum": ["IN_PROGRESS", "FAILED", "COMPLETED"]}, "TextDocument": {"type": "structure", "required": ["relativeFilePath"], "members": {"relativeFilePath": {"shape": "TextDocumentRelativeFilePathString"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "text": {"shape": "TextDocumentTextString"}, "documentSymbols": {"shape": "DocumentSymbols"}}}, "TextDocumentDiagnostic": {"type": "structure", "required": ["document", "range", "source", "severity", "message"], "members": {"document": {"shape": "TextDocument"}, "range": {"shape": "Range"}, "source": {"shape": "SensitiveString"}, "severity": {"shape": "DiagnosticSeverity"}, "message": {"shape": "TextDocumentDiagnosticMessageString"}}}, "TextDocumentDiagnosticMessageString": {"type": "string", "max": 1024, "min": 0, "sensitive": true}, "TextDocumentRelativeFilePathString": {"type": "string", "max": 4096, "min": 1, "sensitive": true}, "TextDocumentTextString": {"type": "string", "max": 40000, "min": 0, "sensitive": true}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "reason": {"shape": "ThrottlingExceptionReason"}}, "exception": true, "retryable": {"throttling": true}}, "ThrottlingExceptionReason": {"type": "string", "enum": ["MONTHLY_REQUEST_COUNT"]}, "Timestamp": {"type": "timestamp"}, "Tool": {"type": "structure", "members": {"toolSpecification": {"shape": "ToolSpecification"}}, "union": true}, "ToolDescription": {"type": "string", "max": 10240, "min": 1, "sensitive": true}, "ToolInputSchema": {"type": "structure", "members": {"json": {"shape": "SensitiveDocument"}}}, "ToolName": {"type": "string", "max": 64, "min": 0, "pattern": "[a-zA-Z][a-zA-Z0-9_]*", "sensitive": true}, "ToolResult": {"type": "structure", "required": ["toolUseId", "content"], "members": {"toolUseId": {"shape": "ToolUseId"}, "content": {"shape": "ToolResultContent"}, "status": {"shape": "ToolResultStatus"}}}, "ToolResultContent": {"type": "list", "member": {"shape": "ToolResultContentBlock"}}, "ToolResultContentBlock": {"type": "structure", "members": {"text": {"shape": "ToolResultContentBlockTextString"}, "json": {"shape": "SensitiveDocument"}}, "union": true}, "ToolResultContentBlockTextString": {"type": "string", "max": 800000, "min": 0, "sensitive": true}, "ToolResultStatus": {"type": "string", "enum": ["success", "error"]}, "ToolResults": {"type": "list", "member": {"shape": "ToolResult"}, "max": 10, "min": 0}, "ToolSpecification": {"type": "structure", "required": ["inputSchema", "name"], "members": {"inputSchema": {"shape": "ToolInputSchema"}, "name": {"shape": "ToolName"}, "description": {"shape": "ToolDescription"}}}, "ToolUse": {"type": "structure", "required": ["toolUseId", "name", "input"], "members": {"toolUseId": {"shape": "ToolUseId"}, "name": {"shape": "ToolName"}, "input": {"shape": "SensitiveDocument"}}}, "ToolUseId": {"type": "string", "max": 64, "min": 0, "pattern": "[a-zA-Z0-9_-]+"}, "ToolUses": {"type": "list", "member": {"shape": "ToolUse"}, "max": 10, "min": 0}, "Tools": {"type": "list", "member": {"shape": "Tool"}}, "TransformEvent": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "TransformationJobId"}, "timestamp": {"shape": "Timestamp"}, "ideCategory": {"shape": "IdeCategory"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "linesOfCodeChanged": {"shape": "Integer"}, "charsOfCodeChanged": {"shape": "Integer"}, "linesOfCodeSubmitted": {"shape": "Integer"}}}, "TransformationDotNetRuntimeEnv": {"type": "string", "enum": ["NET_5_0", "NET_6_0", "NET_7_0", "NET_8_0", "NET_9_0", "NET_STANDARD_2_0"]}, "TransformationDownloadArtifact": {"type": "structure", "members": {"downloadArtifactType": {"shape": "TransformationDownloadArtifactType"}, "downloadArtifactId": {"shape": "ArtifactId"}}}, "TransformationDownloadArtifactType": {"type": "string", "enum": ["ClientInstructions", "Logs", "GeneratedCode"]}, "TransformationDownloadArtifacts": {"type": "list", "member": {"shape": "TransformationDownloadArtifact"}, "max": 10, "min": 0}, "TransformationJavaRuntimeEnv": {"type": "string", "enum": ["JVM_8", "JVM_11", "JVM_17", "JVM_21"]}, "TransformationJob": {"type": "structure", "members": {"jobId": {"shape": "TransformationJobId"}, "transformationSpec": {"shape": "TransformationSpec"}, "status": {"shape": "TransformationStatus"}, "reason": {"shape": "String"}, "creationTime": {"shape": "Timestamp"}, "startExecutionTime": {"shape": "Timestamp"}, "endExecutionTime": {"shape": "Timestamp"}}}, "TransformationJobId": {"type": "string", "max": 128, "min": 1}, "TransformationLanguage": {"type": "string", "enum": ["JAVA_8", "JAVA_11", "JAVA_17", "JAVA_21", "C_SHARP", "COBOL", "PL_I", "JCL"]}, "TransformationLanguages": {"type": "list", "member": {"shape": "TransformationLanguage"}}, "TransformationMainframeRuntimeEnv": {"type": "string", "enum": ["MAINFRAME"]}, "TransformationOperatingSystemFamily": {"type": "string", "enum": ["WINDOWS", "LINUX"]}, "TransformationPlan": {"type": "structure", "required": ["transformationSteps"], "members": {"transformationSteps": {"shape": "TransformationSteps"}}}, "TransformationPlatformConfig": {"type": "structure", "members": {"operatingSystemFamily": {"shape": "TransformationOperatingSystemFamily"}}}, "TransformationProgressUpdate": {"type": "structure", "required": ["name", "status"], "members": {"name": {"shape": "String"}, "status": {"shape": "TransformationProgressUpdateStatus"}, "description": {"shape": "String"}, "startTime": {"shape": "Timestamp"}, "endTime": {"shape": "Timestamp"}, "downloadArtifacts": {"shape": "TransformationDownloadArtifacts"}}}, "TransformationProgressUpdateStatus": {"type": "string", "enum": ["IN_PROGRESS", "COMPLETED", "FAILED", "PAUSED", "AWAITING_CLIENT_ACTION", "SKIPPED"]}, "TransformationProjectArtifactDescriptor": {"type": "structure", "members": {"sourceCodeArtifact": {"shape": "TransformationSourceCodeArtifactDescriptor"}}, "union": true}, "TransformationProjectState": {"type": "structure", "members": {"language": {"shape": "TransformationLanguage"}, "runtimeEnv": {"shape": "TransformationRuntimeEnv"}, "platformConfig": {"shape": "TransformationPlatformConfig"}, "projectArtifact": {"shape": "TransformationProjectArtifactDescriptor"}}}, "TransformationRuntimeEnv": {"type": "structure", "members": {"java": {"shape": "TransformationJavaRuntimeEnv"}, "dotNet": {"shape": "TransformationDotNetRuntimeEnv"}, "mainframe": {"shape": "TransformationMainframeRuntimeEnv"}}, "union": true}, "TransformationSourceCodeArtifactDescriptor": {"type": "structure", "members": {"languages": {"shape": "TransformationLanguages"}, "runtimeEnv": {"shape": "TransformationRuntimeEnv"}}}, "TransformationSpec": {"type": "structure", "members": {"transformationType": {"shape": "TransformationType"}, "source": {"shape": "TransformationProjectState"}, "target": {"shape": "TransformationProjectState"}}}, "TransformationStatus": {"type": "string", "enum": ["CREATED", "ACCEPTED", "REJECTED", "STARTED", "PREPARING", "PREPARED", "PLANNING", "PLANNED", "TRANSFORMING", "TRANSFORMED", "FAILED", "COMPLETED", "PARTIALLY_COMPLETED", "STOPPING", "STOPPED", "PAUSED", "RESUMED"]}, "TransformationStep": {"type": "structure", "required": ["id", "name", "description", "status"], "members": {"id": {"shape": "StepId"}, "name": {"shape": "String"}, "description": {"shape": "String"}, "status": {"shape": "TransformationStepStatus"}, "progressUpdates": {"shape": "ProgressUpdates"}, "startTime": {"shape": "Timestamp"}, "endTime": {"shape": "Timestamp"}}}, "TransformationStepStatus": {"type": "string", "enum": ["CREATED", "COMPLETED", "PARTIALLY_COMPLETED", "STOPPED", "FAILED", "PAUSED", "SKIPPED"]}, "TransformationSteps": {"type": "list", "member": {"shape": "TransformationStep"}}, "TransformationType": {"type": "string", "enum": ["LANGUAGE_UPGRADE", "DOCUMENT_GENERATION"]}, "TransformationUploadArtifactType": {"type": "string", "enum": ["Dependencies", "ClientBuildResult"]}, "TransformationUploadContext": {"type": "structure", "required": ["jobId", "uploadArtifactType"], "members": {"jobId": {"shape": "TransformationJobId"}, "uploadArtifactType": {"shape": "TransformationUploadArtifactType"}}}, "TransformationUserActionStatus": {"type": "string", "enum": ["COMPLETED", "REJECTED"]}, "UUID": {"type": "string", "max": 36, "min": 36}, "UploadContext": {"type": "structure", "members": {"taskAssistPlanningUploadContext": {"shape": "TaskAssistPlanningUploadContext"}, "transformationUploadContext": {"shape": "TransformationUploadContext"}, "codeAnalysisUploadContext": {"shape": "CodeAnalysisUploadContext"}, "codeFixUploadContext": {"shape": "CodeFixUploadContext"}, "workspaceContextUploadContext": {"shape": "WorkspaceContextUploadContext"}}, "union": true}, "UploadId": {"type": "string", "max": 128, "min": 1}, "UploadIntent": {"type": "string", "enum": ["TRANSFORMATION", "TASK_ASSIST_PLANNING", "AUTOMATIC_FILE_SECURITY_SCAN", "FULL_PROJECT_SECURITY_SCAN", "UNIT_TESTS_GENERATION", "CODE_FIX_GENERATION", "WORKSPACE_CONTEXT"]}, "Url": {"type": "string", "max": 1024, "min": 1}, "UserContext": {"type": "structure", "required": ["ideCategory", "operatingSystem", "product"], "members": {"ideCategory": {"shape": "IdeCategory"}, "operatingSystem": {"shape": "OperatingSystem"}, "product": {"shape": "UserContextProductString"}, "clientId": {"shape": "UUID"}, "ideVersion": {"shape": "String"}}}, "UserContextProductString": {"type": "string", "max": 128, "min": 1, "pattern": "[-a-zA-Z0-9._]*"}, "UserInputMessage": {"type": "structure", "required": ["content"], "members": {"content": {"shape": "UserInputMessageContentString"}, "userInputMessageContext": {"shape": "UserInputMessageContext"}, "userIntent": {"shape": "UserIntent"}, "origin": {"shape": "Origin"}, "images": {"shape": "ImageBlocks"}}}, "UserInputMessageContentString": {"type": "string", "max": 600000, "min": 0, "sensitive": true}, "UserInputMessageContext": {"type": "structure", "members": {"editorState": {"shape": "EditorState"}, "shellState": {"shape": "ShellState"}, "gitState": {"shape": "GitState"}, "envState": {"shape": "EnvState"}, "appStudioContext": {"shape": "AppStudioState"}, "diagnostic": {"shape": "Diagnostic"}, "consoleState": {"shape": "ConsoleState"}, "userSettings": {"shape": "UserSettings"}, "additionalContext": {"shape": "AdditionalContentList"}, "toolResults": {"shape": "ToolResults"}, "tools": {"shape": "Tools"}}}, "UserIntent": {"type": "string", "enum": ["SUGGEST_ALTERNATE_IMPLEMENTATION", "APPLY_COMMON_BEST_PRACTICES", "IMPROVE_CODE", "SHOW_EXAMPLES", "CITE_SOURCES", "EXPLAIN_LINE_BY_LINE", "EXPLAIN_CODE_SELECTION", "GENERATE_CLOUDFORMATION_TEMPLATE", "GENERATE_UNIT_TESTS", "CODE_GENERATION"]}, "UserModificationEvent": {"type": "structure", "required": ["sessionId", "requestId", "programmingLanguage", "modificationPercentage", "timestamp", "acceptedCharacterCount", "unmodifiedAcceptedCharacterCount"], "members": {"sessionId": {"shape": "UUID"}, "requestId": {"shape": "UUID"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "modificationPercentage": {"shape": "Double"}, "customizationArn": {"shape": "CustomizationArn"}, "timestamp": {"shape": "Timestamp"}, "acceptedCharacterCount": {"shape": "PrimitiveInteger"}, "unmodifiedAcceptedCharacterCount": {"shape": "PrimitiveInteger"}}}, "UserSettings": {"type": "structure", "members": {"hasConsentedToCrossRegionCalls": {"shape": "Boolean"}}}, "UserTriggerDecisionEvent": {"type": "structure", "required": ["sessionId", "requestId", "programmingLanguage", "completionType", "suggestionState", "recommendationLatencyMilliseconds", "timestamp"], "members": {"sessionId": {"shape": "UUID"}, "requestId": {"shape": "UUID"}, "customizationArn": {"shape": "CustomizationArn"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "completionType": {"shape": "CompletionType"}, "suggestionState": {"shape": "SuggestionState"}, "recommendationLatencyMilliseconds": {"shape": "Double"}, "timestamp": {"shape": "Timestamp"}, "triggerToResponseLatencyMilliseconds": {"shape": "Double"}, "suggestionReferenceCount": {"shape": "PrimitiveInteger"}, "generatedLine": {"shape": "PrimitiveInteger"}, "numberOfRecommendations": {"shape": "PrimitiveInteger"}, "perceivedLatencyMilliseconds": {"shape": "Double"}, "acceptedCharacterCount": {"shape": "PrimitiveInteger"}, "addedIdeDiagnostics": {"shape": "IdeDiagnosticList"}, "removedIdeDiagnostics": {"shape": "IdeDiagnosticList"}}}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "reason": {"shape": "ValidationExceptionReason"}}, "exception": true}, "ValidationExceptionReason": {"type": "string", "enum": ["INVALID_CONVERSATION_ID", "CONTENT_LENGTH_EXCEEDS_THRESHOLD", "INVALID_KMS_GRANT"]}, "WorkspaceContext": {"type": "structure", "required": ["toggle"], "members": {"toggle": {"shape": "OptInFeatureToggle"}}}, "WorkspaceContextUploadContext": {"type": "structure", "required": ["workspaceId", "relativePath", "programmingLanguage"], "members": {"workspaceId": {"shape": "UUID"}, "relativePath": {"shape": "SensitiveString"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}}}, "WorkspaceFolderList": {"type": "list", "member": {"shape": "WorkspaceFolderListMemberString"}, "max": 100, "min": 0}, "WorkspaceFolderListMemberString": {"type": "string", "max": 4096, "min": 1, "sensitive": true}, "WorkspaceList": {"type": "list", "member": {"shape": "WorkspaceMetadata"}}, "WorkspaceMetadata": {"type": "structure", "required": ["workspaceId", "workspaceStatus"], "members": {"workspaceId": {"shape": "UUID"}, "workspaceStatus": {"shape": "WorkspaceStatus"}, "environmentId": {"shape": "SensitiveString"}}}, "WorkspaceState": {"type": "structure", "required": ["uploadId", "programmingLanguage"], "members": {"uploadId": {"shape": "UploadId"}, "programmingLanguage": {"shape": "ProgrammingLanguage"}, "contextTruncationScheme": {"shape": "ContextTruncationScheme"}}}, "WorkspaceStatus": {"type": "string", "enum": ["CREATED", "PENDING", "READY", "CONNECTED", "DELETING"]}, "timeBetweenChunks": {"type": "list", "member": {"shape": "Double"}, "max": 100, "min": 0}}}