<?xml version='1.0' encoding='UTF-8'?>
<SmellBaseline>
  <ManuallySuppressedIssues/>
  <CurrentIssues>
    <ID>CommentWrapping:Attributes.kt$NullAttribute$/*Dynamo always expects the NUL field to contain true */</ID>
    <ID>CommentWrapping:ConfigureMaxResultsAction.kt$ConfigureMaxResultsAction$/* popup */</ID>
    <ID>CommentWrapping:ProjectFileBrowseListener.kt$/* infer disposable from UI context */</ID>
    <ID>CommentWrapping:S3VirtualBucket.kt$S3VirtualBucket$/* Unit tests refuse to open this in an editor if this is true */</ID>
    <ID>CommentWrapping:SamInitSelectionPanel.kt$SamInitSelectionPanel$/* Only available in PyCharm! */</ID>
    <ID>CommentWrapping:SamInitSelectionPanel.kt$SamInitSelectionPanel$/* Used in Rider to refresh the validation */</ID>
    <ID>DestructuringDeclarationWithTooManyEntries:JavaCodeScanSessionConfig.kt$JavaCodeScanSessionConfig$val (sourceFiles, srcPayloadSize, totalLines, buildPaths) = includeDependencies()</ID>
    <ID>ExpressionBodySyntax:CawsProjectListRenderer.kt$CawsProjectListRenderer.&lt;no name provided>$return myContext</ID>
    <ID>ExpressionBodySyntax:CodeWhispererSettingsTest.kt$CodeWhispererSettingsTest.&lt;no name provided>$return myToolWindows[id]</ID>
    <ID>ExpressionBodySyntax:CodeWhispererTelemetryService.kt$CodeWhispererTelemetryService$return previousUserTriggerDecisionTimestamp?.let { Duration.between(it, Instant.now()).toMillis().toDouble() }</ID>
    <ID>Filename:AwsSettingsPanel.kt$software.aws.toolkits.jetbrains.core.credentials.AwsSettingsPanel.kt</ID>
    <ID>Filename:CawsSpaceProjectInfo.kt$software.aws.toolkits.jetbrains.services.caws.CawsSpaceProjectInfo.kt</ID>
    <ID>Filename:ShowLogsAroundAction.kt$software.aws.toolkits.jetbrains.services.cloudwatch.logs.actions.ShowLogsAroundAction.kt</ID>
    <ID>FunctionNaming:AwsSettingsConfigurableTest.kt$AwsSettingsConfigurableTest$@Test fun validate_fail_autodetectBadSam_andManuallySetToBadSam()</ID>
    <ID>FunctionNaming:AwsSettingsConfigurableTest.kt$AwsSettingsConfigurableTest$@Test fun validate_ok_autodetectBadSam()</ID>
    <ID>FunctionNaming:AwsSettingsConfigurableTest.kt$AwsSettingsConfigurableTest$@Test fun validate_ok_autodetectValidSam()</ID>
    <ID>FunctionNaming:AwsSettingsConfigurableTest.kt$AwsSettingsConfigurableTest$@Test fun validate_ok_changedTelemetry()</ID>
    <ID>FunctionNaming:AwsSettingsConfigurableTest.kt$AwsSettingsConfigurableTest$@Test fun validate_ok_noOp()</ID>
    <ID>FunctionNaming:AwsSettingsConfigurableTest.kt$AwsSettingsConfigurableTest$@Test fun validate_ok_setSamEmpty()</ID>
    <ID>FunctionNaming:AwsSettingsConfigurableTest.kt$AwsSettingsConfigurableTest$@Test fun validate_ok_setValidSam()</ID>
    <ID>FunctionNaming:AwsSettingsConfigurableTest.kt$AwsSettingsConfigurableTest$@Test(expected = ConfigurationException::class) fun validate_fail_setBadSam()</ID>
    <ID>FunctionNaming:CloudFormationParametersTest.kt$CloudFormationParametersTest$@Test fun mergeParameters_emptyRemote()</ID>
    <ID>FunctionNaming:CloudFormationParametersTest.kt$CloudFormationParametersTest$@Test fun mergeParameters_emptyTemplate()</ID>
    <ID>FunctionNaming:CloudFormationParametersTest.kt$CloudFormationParametersTest$@Test fun mergeParameters_withOverlap()</ID>
    <ID>FunctionNaming:CloudFormationTemplateCanDeployTest.kt$CloudFormationTemplateCanDeployTest$@Test fun deployable_validatableEnough()</ID>
    <ID>FunctionNaming:CloudFormationTemplateCanDeployTest.kt$CloudFormationTemplateCanDeployTest$@Test fun nonDeployable_emptyFile()</ID>
    <ID>FunctionNaming:CloudFormationTemplateCanDeployTest.kt$CloudFormationTemplateCanDeployTest$@Test fun nonDeployable_incompleteResources()</ID>
    <ID>FunctionNaming:CloudFormationTemplateIndexTest.kt$CloudFormationTemplateIndexTest$@Test fun listFunctions_lambdaFunction()</ID>
    <ID>FunctionNaming:CloudFormationTemplateIndexTest.kt$CloudFormationTemplateIndexTest$@Test fun listFunctions_missingType()</ID>
    <ID>FunctionNaming:CloudFormationTemplateIndexTest.kt$CloudFormationTemplateIndexTest$@Test fun listFunctions_serverlessAndLambdaFunctions()</ID>
    <ID>FunctionNaming:CloudFormationTemplateIndexTest.kt$CloudFormationTemplateIndexTest$@Test fun listFunctions_serverlessFunction()</ID>
    <ID>FunctionNaming:CloudFormationTemplateIndexTest.kt$CloudFormationTemplateIndexTest$@Test fun listResourcesByType_simpleTable()</ID>
    <ID>FunctionNaming:CloudFormationTemplateIndexTest.kt$CloudFormationTemplateIndexTest$@Test fun listResources_fromFile()</ID>
    <ID>FunctionNaming:CloudFormationTemplateIndexTest.kt$CloudFormationTemplateIndexTest$@Test fun listResources_nullType()</ID>
    <ID>FunctionNaming:CreateBucketActionDialogTest.kt$CreateBucketActionDialogTest$@Test fun validateBucketName_emptyBucketName()</ID>
    <ID>FunctionNaming:CreateLambdaFunctionActionTest.kt$CreateLambdaFunctionActionTest$@Test fun InvalidNullArgs()</ID>
    <ID>FunctionNaming:CreateLambdaFunctionActionTest.kt$CreateLambdaFunctionActionTest$@Test fun InvalidNullArgs_Element()</ID>
    <ID>FunctionNaming:CreateLambdaFunctionActionTest.kt$CreateLambdaFunctionActionTest$@Test fun InvalidNullArgs_HandlerResolver()</ID>
    <ID>FunctionNaming:CreateLambdaFunctionActionTest.kt$CreateLambdaFunctionActionTest$@Test fun NonSamFunction()</ID>
    <ID>FunctionNaming:CreateLambdaFunctionActionTest.kt$CreateLambdaFunctionActionTest$@Test fun NonSamFunction_Substring()</ID>
    <ID>FunctionNaming:CreateLambdaFunctionActionTest.kt$CreateLambdaFunctionActionTest$@Test fun SamFunction()</ID>
    <ID>FunctionNaming:DeleteWaiterTest.kt$DeleteWaiterTest$@Test fun deleteSuccessful_stackNotExist()</ID>
    <ID>FunctionNaming:DeploySettingsTest.kt$DeploySettingsTest$@Test fun relativeSamPath_null()</ID>
    <ID>FunctionNaming:DeploySettingsTest.kt$DeploySettingsTest$@Test fun relativeSamPath_root()</ID>
    <ID>FunctionNaming:FileInfoCacheTest.kt$FileInfoCacheTest$@Test fun emptyCache_SingleExecutableRequest()</ID>
    <ID>FunctionNaming:FileInfoCacheTest.kt$FileInfoCacheTest$@Test fun multipleThreads_SameSamPath()</ID>
    <ID>FunctionNaming:FileInfoCacheTest.kt$FileInfoCacheTest$@Test fun nonEmptyCache_SingleExecutableRequest()</ID>
    <ID>FunctionNaming:RetrieveSavedQueryDialogTest.kt$RetrieveSavedQueryDialogTest$@Test fun populateParentEditor_noLogGroups()</ID>
    <ID>FunctionNaming:RetrieveSavedQueryDialogTest.kt$RetrieveSavedQueryDialogTest$@Test fun populateParentEditor_withLogGroups()</ID>
    <ID>FunctionNaming:SamCommonTest.kt$SamCommonTest$@Test fun getCodeUri_multipleUris()</ID>
    <ID>FunctionNaming:SamCommonTest.kt$SamCommonTest$@Test fun getCodeUri_noUri()</ID>
    <ID>FunctionNaming:SamCommonTest.kt$SamCommonTest$@Test fun getCodeUri_samAndNotSam()</ID>
    <ID>FunctionNaming:SamCommonTest.kt$SamCommonTest$@Test fun getCodeUri_singleUri()</ID>
    <ID>FunctionNaming:SamCommonTest.kt$SamCommonTest$@Test fun getTemplateFromDirectory_singleYaml()</ID>
    <ID>FunctionNaming:SamCommonTest.kt$SamCommonTest$@Test fun getVersion_Valid_exitNonZero()</ID>
    <ID>FunctionNaming:SamCommonTest.kt$SamCommonTest$@Test fun getVersion_badPath()</ID>
    <ID>FunctionNaming:SamCommonTest.kt$SamCommonTest$@Test(expected = java.lang.AssertionError::class) fun getTemplateFromDirectory_multipleYaml()</ID>
    <ID>FunctionNaming:SamCommonTest.kt$SamCommonTest$@Test(expected = java.lang.AssertionError::class) fun getTemplateFromDirectory_noYaml()</ID>
    <ID>FunctionNaming:SamVersionCacheTest.kt$SamVersionCacheTest$@Test fun errorCode_InvalidOption()</ID>
    <ID>FunctionNaming:SamVersionCacheTest.kt$SamVersionCacheTest$@Test fun errorCode_RandomError()</ID>
    <ID>FunctionNaming:SamVersionCacheTest.kt$SamVersionCacheTest$@Test fun successExecution_EmptyOutput()</ID>
    <ID>FunctionOnlyReturningConstant:CodeWhispererCodeScanTestBase.kt$CodeWhispererCodeScanTestBase$protected fun getFakeRecommendationsOnNonExistentFile()</ID>
    <ID>LoopWithTooManyJumpStatements:DownloadObjectAction.kt$DownloadObjectAction$for</ID>
    <ID>TopLevelPropertyNaming:EventsFetcherTest.kt$private const val nonEmptyMessage = "Second call on the same page must not return anything"</ID>
    <ID>TopLevelPropertyNaming:EventsFetcherTest.kt$private const val wrongPageMessage = "Wrong list of available pages"</ID>
    <ID>TopLevelPropertyNaming:SqsUtils.kt$const val sqsPolicyStatementArray = "Statement"</ID>
    <ID>UnusedPrivateProperty:CawsCloneDialogComponent.kt$CawsCloneDialogComponent$private val modalityState: ModalityState</ID>
    <ID>UseCheckOrError:AwsConsoleUrlFactory.kt$AwsConsoleUrlFactory$throw IllegalStateException("Partition '${region.partitionId}' is not supported")</ID>
    <ID>UseCheckOrError:AwsRegionProviderTest.kt$AwsRegionProviderTest$throw IllegalStateException("Bad test data")</ID>
    <ID>UseCheckOrError:CawsParameterDescriptions.kt$throw IllegalStateException("Failed to locate parameterDescriptions.json")</ID>
    <ID>UseCheckOrError:CloudWatchActor.kt$CloudWatchLogsActor$throw IllegalStateException("Table does not support loadInitial")</ID>
    <ID>UseCheckOrError:CloudWatchActor.kt$CloudWatchLogsActor$throw IllegalStateException("Table does not support loadInitialFilter")</ID>
    <ID>UseCheckOrError:CloudWatchActor.kt$CloudWatchLogsActor$throw IllegalStateException("Table does not support loadInitialRange")</ID>
    <ID>UseCheckOrError:CreateFunctionDialog.kt$CreateFunctionDialog$throw IllegalStateException("Failed to locate module for $element")</ID>
    <ID>UseCheckOrError:CreateFunctionDialog.kt$CreateFunctionDialog$throw IllegalStateException("LambdaBuilder for $runtime not found")</ID>
    <ID>UseCheckOrError:CreateFunctionDialog.kt$CreateFunctionDialog$throw IllegalStateException("Runtime is missing when package type is Zip")</ID>
    <ID>UseCheckOrError:CreationDialog.kt$CreationDialog$throw IllegalStateException("AppRunner creation dialog had no type selected!")</ID>
    <ID>UseCheckOrError:CredentialChoice.kt$CredentialProviderSelector2$throw IllegalStateException("Can't get credential identifier when the selection is an invalid one")</ID>
    <ID>UseCheckOrError:DataContextUtils.kt$throw IllegalStateException("Required dataId '${dataId.name}` was missing")</ID>
    <ID>UseCheckOrError:DetailedLogRecord.kt$DetailedLogRecord.Companion$throw IllegalStateException("$log format does not appear to be in a valid format (&lt;account-id>:&lt;log-group-name>)")</ID>
    <ID>UseCheckOrError:DownloadLogStream.kt$LogStreamDownloadToFileTask.&lt;no name provided>$throw IllegalStateException("Log Stream was downloaded but does not exist on disk!")</ID>
    <ID>UseCheckOrError:HandlerCompletionProvider.kt$HandlerCompletionProvider$throw IllegalStateException("handlerCompletion must be defined if completion is enabled.")</ID>
    <ID>UseCheckOrError:HandlerPanel.kt$HandlerPanel$throw IllegalStateException("Runtime was not set in the HandlerPanel")</ID>
    <ID>UseCheckOrError:InsightsUtils.kt$throw IllegalStateException("CWL GetQueryResults returned record without @ptr field")</ID>
    <ID>UseCheckOrError:JavaDebugSupport.kt$throw IllegalStateException("Attaching to the JVM failed! $debugHost:${debugPorts.first()}")</ID>
    <ID>UseCheckOrError:JavaTestUtils.kt$throw IllegalStateException("Failed to locate $it")</ID>
    <ID>UseCheckOrError:JavaTestUtils.kt$throw IllegalStateException("Failed to locate gradlew")</ID>
    <ID>UseCheckOrError:LambdaBuilder.kt$LambdaBuilder$throw IllegalStateException("Cannot map runtime $runtime to SDK runtime.")</ID>
    <ID>UseCheckOrError:LambdaBuilder.kt$LambdaBuilder.Companion$throw IllegalStateException("Failed to locate module for ${psiFile.virtualFile}")</ID>
    <ID>UseCheckOrError:LambdaConfigPanel.kt$LambdaConfigPanel$throw IllegalStateException("Unsupported package type ${packageType()}")</ID>
    <ID>UseCheckOrError:LambdaUtils.kt$throw IllegalStateException("$this has bad minSamDebuggingVersion! It should be a semver string!")</ID>
    <ID>UseCheckOrError:LambdaUtils.kt$throw IllegalStateException("$this has bad minSamInitVersion! It should be a semver string!")</ID>
    <ID>UseCheckOrError:LambdaWorkflows.kt$throw IllegalStateException("Tried to update a lambda without valid AWS connection")</ID>
    <ID>UseCheckOrError:LocalLambdaRunConfiguration.kt$LocalLambdaRunConfiguration$throw IllegalStateException("Cannot map runtime $runtime to SDK runtime.")</ID>
    <ID>UseCheckOrError:LocalLambdaRunConfiguration.kt$LocalLambdaRunConfiguration$throw IllegalStateException("Function ${logicalId()} not found in template!")</ID>
    <ID>UseCheckOrError:LocalLambdaRunConfiguration.kt$LocalLambdaRunConfiguration$throw IllegalStateException("Image functions must be a SAM function")</ID>
    <ID>UseCheckOrError:LocalLambdaRunConfiguration.kt$LocalLambdaRunConfiguration$throw IllegalStateException("No image debugger with ID ${rawImageDebugger()}")</ID>
    <ID>UseCheckOrError:LocalLambdaRunConfiguration.kt$LocalLambdaRunConfiguration$throw IllegalStateException("Unable to get virtual file for path $dockerFilePath")</ID>
    <ID>UseCheckOrError:LocalLambdaRunSettings.kt$HandlerRunSettings$throw IllegalStateException("Attempting to run SAM for unsupported runtime $runtime")</ID>
    <ID>UseCheckOrError:LocalLambdaRunSettings.kt$ImageTemplateRunSettings$throw IllegalStateException("Attempting to run SAM for unsupported language ${imageDebugger.languageId}")</ID>
    <ID>UseCheckOrError:LocalLambdaRunSettings.kt$TemplateRunSettings$throw IllegalStateException("Attempting to run SAM for unsupported runtime $runtime")</ID>
    <ID>UseCheckOrError:LocalLambdaRunSettings.kt$throw IllegalStateException("Can't find debugger support for $this")</ID>
    <ID>UseCheckOrError:OpenShellInContainerDialog.kt$OpenShellInContainerDialog$throw IllegalStateException("Task not Selected")</ID>
    <ID>UseCheckOrError:PushToRepositoryAction.kt$PushToEcrDialog$throw IllegalStateException("image id was null")</ID>
    <ID>UseCheckOrError:PushToRepositoryAction.kt$PushToEcrDialog$throw IllegalStateException("repository uri was null")</ID>
    <ID>UseCheckOrError:PushToRepositoryAction.kt$PushToEcrDialog$throw IllegalStateException("run configuration was null")</ID>
    <ID>UseCheckOrError:PushToRepositoryAction.kt$PushToEcrDialog$throw IllegalStateException()</ID>
    <ID>UseCheckOrError:PythonLambdaBuilder.kt$PythonLambdaBuilder.Companion$throw IllegalStateException("Cannot locate requirements.txt in a parent directory of ${startLocation.path}")</ID>
    <ID>UseCheckOrError:PythonLambdaHandlerResolver.kt$PythonLambdaHandlerResolver$throw IllegalStateException("Failed to locate requirements.txt")</ID>
    <ID>UseCheckOrError:RemoteLambdaRunSettingsEditor.kt$RemoteLambdaRunSettingsEditor$throw IllegalStateException("functionSelector.reload() called before region/credentials set")</ID>
    <ID>UseCheckOrError:Resources.kt$SamFunction$throw IllegalStateException("Bad packageType somehow returned to code location: ${packageType()}")</ID>
    <ID>UseCheckOrError:RetrieveSavedQueryDialog.kt$RetrieveSavedQueryDialog.&lt;no name provided>$throw IllegalStateException("No query definition was selected")</ID>
    <ID>UseCheckOrError:RunCommandDialog.kt$RunCommandDialog$throw IllegalStateException("Task not Selected")</ID>
    <ID>UseCheckOrError:RunWithRealCredentials.kt$RunWithRealCredentials.&lt;no name provided>$throw IllegalStateException("Can't locate us-west-2")</ID>
    <ID>UseCheckOrError:RunWithRealCredentials.kt$RunWithRealCredentials.&lt;no name provided>$throw IllegalStateException("RunWithRealCredentials requires a default AWS profile!")</ID>
    <ID>UseCheckOrError:RuntimeGroup.kt$RuntimeGroup.Companion$throw IllegalStateException("No RuntimeGroup with id '$id' is registered")</ID>
    <ID>UseCheckOrError:RuntimeGroup.kt$RuntimeGroupExtensionPointObject$throw IllegalStateException("Attempted to retrieve feature for unsupported runtime group $runtimeGroup")</ID>
    <ID>UseCheckOrError:S3TreeNode.kt$S3TreeNode$throw IllegalStateException("$key has no parent!")</ID>
    <ID>UseCheckOrError:SamInitSelectionPanel.kt$SamInitSelectionPanel$throw IllegalStateException("SemVer is invalid even with valid SAM executable")</ID>
    <ID>UseCheckOrError:SamProjectWizard.kt$SamAppTemplateBased$throw IllegalStateException("Unknown packaging type: $packagingType")</ID>
    <ID>UseCheckOrError:SamTemplateUtils.kt$SamTemplateUtils$throw IllegalStateException("$codeUri does not follow the format $S3_URI_PREFIX&lt;bucket>/&lt;key>")</ID>
    <ID>UseCheckOrError:SamTemplateUtils.kt$SamTemplateUtils$throw IllegalStateException("$codeUri does not start with $S3_URI_PREFIX")</ID>
    <ID>UseCheckOrError:SamTemplateUtils.kt$SamTemplateUtils$throw IllegalStateException("Unable to parse codeUri $codeUri")</ID>
    <ID>UseCheckOrError:SchemaCodeDownloader.kt$SchemaCodeDownloader.Companion$throw IllegalStateException("Attempting to use SchemaCodeDownload without valid AWS connection")</ID>
    <ID>UseCheckOrError:SchemaSelectionPanel.kt$SchemaSelectionPanel$throw IllegalStateException("Schemas is not supported by $this")</ID>
    <ID>UseCheckOrError:SingleS3ObjectAction.kt$SingleS3ObjectAction$throw IllegalStateException("SingleActionNode should only have a single node, got $nodes")</ID>
    <ID>UseCheckOrError:SqsWindowFactory.kt$SqsWindowFactory.Companion$throw IllegalStateException("Can't find tool window $TOOL_WINDOW_ID")</ID>
    <ID>UseCheckOrError:SsmPlugin.kt$SsmPlugin$throw IllegalStateException("Failed to extract $displayName\nSTDOUT:${processOutput.stdout}\nSTDERR:${processOutput.stderr}")</ID>
    <ID>UseCheckOrError:SsmPlugin.kt$SsmPlugin$throw IllegalStateException("Failed to find compatible SSM plugin: SystemInfo=${SystemInfo.OS_NAME}, Arch=${SystemInfo.OS_ARCH}")</ID>
    <ID>UseCheckOrError:SsmPlugin.kt$SsmPlugin$throw IllegalStateException("Failed to locate $executableName under $installDir")</ID>
    <ID>UseCheckOrError:SsmPlugin.kt$SsmPlugin$throw IllegalStateException("Unknown extension $extension")</ID>
    <ID>UseCheckOrError:ToolType.kt$BaseToolType$throw IllegalStateException("Failed to determine version of ${SsmPlugin.displayName}")</ID>
    <ID>UseCheckOrError:ToolkitToolWindow.kt$ToolkitToolWindow$throw IllegalStateException("Can't find tool window $toolWindowId")</ID>
    <ID>UseCheckOrError:UpdateFunctionCodeDialog.kt$UpdateFunctionCodeDialog$throw IllegalStateException("Failed to locate module for $element")</ID>
    <ID>UseCheckOrError:UpdateFunctionCodeDialog.kt$UpdateFunctionCodeDialog$throw IllegalStateException("LambdaBuilder for ${initialSettings.runtime} not found")</ID>
    <ID>UseCheckOrError:UpdateFunctionCodeDialog.kt$UpdateFunctionCodeDialog$throw IllegalStateException("Runtime is missing when package type is Zip")</ID>
    <ID>UseCheckOrError:UpdateFunctionCodePanel.kt$UpdateFunctionCodePanel$throw IllegalStateException("Unsupported package type $packageType")</ID>
    <ID>UseRequire:AwsSettingsConfigurable.kt$AwsSettingsConfigurable$throw IllegalArgumentException("Set file is not an executable")</ID>
    <ID>UseRequire:FourPartVersion.kt$FourPartVersion.Companion$throw IllegalArgumentException("[$version] not in the format of MAJOR.MINOR.PATCH.BUILD")</ID>
    <ID>UseRequire:SemanticVersion.kt$SemanticVersion.Companion$throw IllegalArgumentException("[$version] not in the format of MAJOR.MINOR.PATCH")</ID>
  </CurrentIssues>
</SmellBaseline>
