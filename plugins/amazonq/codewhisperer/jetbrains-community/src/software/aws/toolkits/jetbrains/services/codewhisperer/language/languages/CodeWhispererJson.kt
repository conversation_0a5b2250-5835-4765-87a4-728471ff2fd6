// Copyright 2023 Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: Apache-2.0

package software.aws.toolkits.jetbrains.services.codewhisperer.language.languages

import software.aws.toolkits.jetbrains.services.codewhisperer.language.CodeWhispererProgrammingLanguage
import software.aws.toolkits.telemetry.CodewhispererLanguage

class CodeWhispererJson private constructor() : CodeWhispererProgrammingLanguage() {
    override val languageId: String = ID

    override fun toTelemetryType(): CodewhispererLanguage = CodewhispererLanguage.Json

    override fun isAutoFileScanSupported(): Boolean = true

    override fun lineCommentPrefix() = null

    override fun blockCommentPrefix() = null

    override fun blockCommentSuffix() = null

    companion object {
        const val ID = "json"

        val INSTANCE = CodeWhispererJson()
    }
}
