// Copyright 2025 Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: Apache-2.0

package software.aws.toolkits.jetbrains.services.amazonq.lsp.model.aws.chat

const val AUTH_FOLLOW_UP_CLICKED = "authFollowUpClicked"
const val CHAT_BUTTON_CLICK = "aws/chat/buttonClick"
const val CHAT_CONVERSATION_CLICK = "aws/chat/conversationClick"
const val CHAT_COPY_CODE_TO_CLIPBOARD = "copyToClipboard"
const val CHAT_COPY_CODE_TO_CLIPBOARD_NOTIFICATION = "aws/chat/copyCodeToClipboard"
const val CHAT_CREATE_PROMPT = "aws/chat/createPrompt"
const val CHAT_DISCLAIMER_ACKNOWLEDGED = "disclaimerAcknowledged"
const val CHAT_ERROR_PARAMS = "errorMessage"
const val CHAT_FEEDBACK = "aws/chat/feedback"
const val CHAT_FILE_CLICK = "aws/chat/fileClick"
const val CHAT_FOLLOW_UP_CLICK = "aws/chat/followUpClick"
const val CHAT_INFO_LINK_CLICK = "aws/chat/infoLinkClick"
const val CHAT_INSERT_TO_CURSOR = "insertToCursorPosition"
const val CHAT_INSERT_TO_CURSOR_NOTIFICATION = "aws/chat/insertToCursorPosition"
const val CHAT_LINK_CLICK = "aws/chat/linkClick"
const val CHAT_LIST_CONVERSATIONS = "aws/chat/listConversations"
const val CHAT_OPEN_TAB = "aws/chat/openTab"
const val CHAT_OPTIONS_UPDATE_NOTIFICATION = "aws/chat/chatOptionsUpdate"
const val CHAT_PROMPT_OPTION_ACKNOWLEDGED = "chatPromptOptionAcknowledged"
const val CHAT_QUICK_ACTION = "aws/chat/sendChatQuickAction"
const val CHAT_READY = "aws/chat/ready"
const val CHAT_SEND_CONTEXT_COMMANDS = "aws/chat/sendContextCommands"
const val CHAT_SEND_UPDATE = "aws/chat/sendChatUpdate"
const val CHAT_SOURCE_LINK_CLICK = "aws/chat/sourceLinkClick"
const val CHAT_TAB_ADD = "aws/chat/tabAdd"
const val CHAT_TAB_BAR_ACTIONS = "aws/chat/tabBarAction"
const val CHAT_TAB_CHANGE = "aws/chat/tabChange"
const val CHAT_TAB_REMOVE = "aws/chat/tabRemove"

const val DID_COPY_FILE = "aws/didCopyFile"
const val DID_WRITE_FILE = "aws/didWriteFile"
const val DID_APPEND_FILE = "aws/didAppendFile"
const val DID_REMOVE_FILE = "aws/didRemoveFileOrDirectory"
const val DID_CREATE_DIRECTORY = "aws/didCreateDirectory"

const val GET_SERIALIZED_CHAT_REQUEST_METHOD = "aws/chat/getSerializedChat"
const val GENERIC_COMMAND = "genericCommand"

const val OPEN_FILE_DIFF = "aws/openFileDiff"
const val OPEN_SETTINGS = "openSettings"

const val PROMPT_INPUT_OPTIONS_CHANGE = "aws/chat/promptInputOptionChange"

const val SEND_CHAT_COMMAND_PROMPT = "aws/chat/sendChatPrompt"
const val SHOW_SAVE_FILE_DIALOG_REQUEST_METHOD = "aws/showSaveFileDialog"
const val STOP_CHAT_RESPONSE = "stopChatResponse"
const val SEND_TO_PROMPT = "sendToPrompt"
const val TELEMETRY_EVENT = "telemetry/event"
