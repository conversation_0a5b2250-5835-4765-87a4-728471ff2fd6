<!-- Copyright 2024 Amazon.com, Inc. or its affiliates. All Rights Reserved. -->
<!-- SPDX-License-Identifier: Apache-2.0 -->
<idea-plugin>
    <extensionPoints>
        <extensionPoint qualifiedName="amazon.q.appFactory" interface="software.aws.toolkits.jetbrains.services.amazonq.apps.AmazonQAppFactory" dynamic="true" />
    </extensionPoints>

    <projectListeners>
        <listener class="software.aws.toolkits.jetbrains.services.amazonq.toolwindow.AmazonQToolWindowListener"
                  topic="com.intellij.openapi.wm.ex.ToolWindowManagerListener"/>
    </projectListeners>

    <extensions defaultExtensionNs="com.intellij">
        <!--Q Getting Started page -->
        <fileEditorProvider implementation="software.aws.toolkits.jetbrains.services.amazonq.gettingstarted.QGettingStartedEditorProvider"/>

        <toolWindow id="amazon.q.window" anchor="right"
                    factoryClass="software.aws.toolkits.jetbrains.services.amazonq.toolwindow.AmazonQToolWindowFactory" icon="AwsIcons.Logos.AWS_Q" />

        <postStartupActivity implementation="software.aws.toolkits.jetbrains.services.amazonq.startup.AmazonQStartupActivity"/>
        <actionPromoter order="last" implementation="software.aws.toolkits.jetbrains.services.cwc.inline.InlineChatActionPromoter"/>
    </extensions>

    <extensions defaultExtensionNs="amazon.q">
        <appFactory implementation="software.aws.toolkits.jetbrains.services.cwc.AppFactory" />
        <appFactory implementation="software.aws.toolkits.jetbrains.services.amazonqFeatureDev.FeatureDevAppFactory" />
        <appFactory implementation="software.aws.toolkits.jetbrains.services.amazonqDoc.DocAppFactory" />
        <appFactory implementation="software.aws.toolkits.jetbrains.services.amazonqCodeScan.CodeScanChatAppFactory" />
        <appFactory implementation="software.aws.toolkits.jetbrains.services.amazonqCodeTest.CodeTestChatAppFactory" />
    </extensions>

    <actions>
        <action id="aws.toolkit.open.q.window" class="software.aws.toolkits.jetbrains.services.amazonq.QRefreshPanelAction"/>
        <group id="aws.q.toolwindow.titleBar" popup="false" compact="true">
            <reference id="aws.toolkit.open.q.window"/>
        </group>
        <!-- TODO: q.openchat will eventually be in amazonq, aws.toolkit.q.sign.in will eventually be in core. -->
        <action id="q.openchat" class="software.aws.toolkits.jetbrains.services.amazonq.QOpenPanelAction"/>

        <group id="aws.toolkit.q.sign.in">
            <action id="q.sign.in" class="software.aws.toolkits.jetbrains.services.amazonq.explorerActions.SignInToQAction"/>
            <reference ref="q.learn.more"/>
        </group>

        <group
            id="aws.toolkit.jetbrains.core.services.cwc.actions.ContextMenuActions"
            class="software.aws.toolkits.jetbrains.services.cwc.commands.SendToQActionGroup"
            popup="true"
        >
            <add-to-group
                group-id="EditorPopupMenu"
                anchor="last"
            />

            <action id="aws.toolkit.jetbrains.core.services.cwc.commands.ExplainCodeAction"
                    class="software.aws.toolkits.jetbrains.services.cwc.commands.ExplainCodeAction">
                <keyboard-shortcut keymap="$default" first-keystroke="meta alt E" />
            </action>

            <action id="aws.toolkit.jetbrains.core.services.cwc.commands.RefactorCodeAction"
                    class="software.aws.toolkits.jetbrains.services.cwc.commands.RefactorCodeAction">
                <keyboard-shortcut keymap="$default" first-keystroke="meta alt U" />
            </action>

            <action id="aws.toolkit.jetbrains.core.services.cwc.commands.FixCodeAction"
                    class="software.aws.toolkits.jetbrains.services.cwc.commands.FixCodeAction">
                <keyboard-shortcut keymap="$default" first-keystroke="meta alt Y" />
            </action>

            <action id="aws.toolkit.jetbrains.core.services.cwc.commands.OptimizeCodeAction"
                    class="software.aws.toolkits.jetbrains.services.cwc.commands.OptimizeCodeAction">
                <keyboard-shortcut keymap="$default" first-keystroke="meta alt A" />
            </action>

            <action id="aws.toolkit.jetbrains.core.services.cwc.commands.GenerateUnitTestsAction"
                    class="software.aws.toolkits.jetbrains.services.cwc.commands.GenerateUnitTestsAction">
                <keyboard-shortcut keymap="$default" first-keystroke="meta alt T" />
            </action>

            <action id="aws.toolkit.jetbrains.core.services.cwc.commands.SendToPromptAction"
                    class="software.aws.toolkits.jetbrains.services.cwc.commands.SendToPromptAction">
                <keyboard-shortcut keymap="$default" first-keystroke="meta alt S" />
            </action>

            <action id="aws.toolkit.jetbrains.core.services.cwc.inline.openChat"
                    class="software.aws.toolkits.jetbrains.services.cwc.inline.OpenChatInputAction">
                <keyboard-shortcut keymap="Mac OS X" first-keystroke="meta I"/>
                <keyboard-shortcut keymap="Mac OS X 10.5+" first-keystroke="meta I"/>
                <keyboard-shortcut keymap="$default" first-keystroke="control I"/>
            </action>
        </group>
    </actions>
</idea-plugin>
