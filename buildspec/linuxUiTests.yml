version: 0.2

cache:
  paths:
#    - '/root/.gradle/caches/**/*'
#    - '/root/.gradle/wrapper/**/*'

env:
  variables:
    CI: true
    LOCAL_ENV_RUN: true
    AWS_STS_REGIONAL_ENDPOINTS: regional

    DISPLAY: :99
    SCREEN_WIDTH: 1920
    SCREEN_HEIGHT: 1080
    SCREEN_DEPTH: 24

phases:
  install:
    commands:
      - dnf install -y marco mate-media e2fsprogs
      - startDesktop.sh
      - export PATH="$PATH:$HOME/.dotnet/tools"
      - dotnet codeartifact-creds install

  build:
    commands:
      - |
        if [ "$CODEARTIFACT_DOMAIN_NAME" ] && [ "$CODEARTIFACT_REPO_NAME" ]; then
          CODEARTIFACT_URL=$(aws codeartifact get-repository-endpoint --domain $CODEARTIFACT_DOMAIN_NAME --repository $CODEARTIFACT_REPO_NAME --format maven --query repositoryEndpoint --output text)
          CODEARTIFACT_NUGET_URL=$(aws codeartifact get-repository-endpoint --domain $CODEARTIFACT_DOMAIN_NAME --repository $CODEARTIFACT_REPO_NAME --format nuget --query repositoryEndpoint --output text) 
          CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain $CODEARTIFACT_DOMAIN_NAME --query authorizationToken --output text --duration-seconds 3600)
        fi

      - AWS_CONFIG_FILE=`mktemp`
      - |        
        >$AWS_CONFIG_FILE echo "[default]
        role_arn=$ASSUME_ROLE_ARN
        credential_source=EcsContainer"
        
      - chmod +x gradlew

      - ffmpeg -loglevel quiet -nostdin -f x11grab -video_size ${SCREEN_WIDTH}x${SCREEN_HEIGHT} -i ${DISPLAY} -codec:v libx264 -pix_fmt yuv420p -vf drawtext="fontsize=48:box=1:boxcolor=black@0.75:boxborderw=5:fontcolor=white:x=0:y=h-text_h:text='%{gmtime\:%H\\\\\:%M\\\\\:%S}'" -framerate 12 -g 12 /tmp/screen_recording.mp4 &
      - ./gradlew -PideProfileName=$ALTERNATIVE_IDE_PROFILE_NAME :ui-tests-starter:uiTest coverageReport --console plain --info

  post_build:
    commands:
      - TEST_ARTIFACTS="/tmp/testArtifacts"
      - mkdir -p $TEST_ARTIFACTS/test-reports

      - pkill -SIGINT ffmpeg && sleep 5

      - rsync -rmq --include='*/' --include '**/build/idea-sandbox/**/log*/**' --exclude='*' . $TEST_ARTIFACTS/ || true
      - rsync -rmq --include='*/' --include '**/build/reports/**' --exclude='*' . $TEST_ARTIFACTS/ || true
      - rsync -rmq --include='*/' --include '**/test-results/**/*.xml' --exclude='*' . $TEST_ARTIFACTS/test-reports || true

      - mv /tmp/screen_recording.mp4 $TEST_ARTIFACTS/

      - VCS_COMMIT_ID="${CODEBUILD_RESOLVED_SOURCE_VERSION}"
      - CI_BUILD_URL=$(echo $CODEBUILD_BUILD_URL | sed 's/#/%23/g') # Encode `#` in the URL because otherwise the url is clipped in the Codecov.io site
      - CI_BUILD_ID="${CODEBUILD_BUILD_ID}"
      - test -n "$CODE_COV_TOKEN" && curl -Os https://uploader.codecov.io/latest/linux/codecov && chmod +x codecov || true # this sometimes times out but we don't want to fail the build
      - test -n "$CODE_COV_TOKEN" && test -n "$CODEBUILD_BUILD_SUCCEEDING" && ./codecov -t $CODE_COV_TOKEN -F uitest || true

reports:
  ui-test:
    files:
      - "**/*"
    base-directory: /tmp/testArtifacts/test-reports
    discard-paths: yes

artifacts:
  base-directory: /tmp/testArtifacts
  files:
    - "**/*"
