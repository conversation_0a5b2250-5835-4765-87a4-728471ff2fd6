on:
  push:
    branches: [main]
  pull_request:
    # By default, CI will trigger on opened/synchronize/reopened event types.
    # https://docs.github.com/en/actions/writing-workflows/choosing-when-your-workflow-runs/events-that-trigger-workflows#pull_request
    # Note: To re-run `lint-commits` after fixing the PR title, close-and-reopen the PR.
    branches: [main, feature/*]

# Cancel old jobs when a pull request is updated.
concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  lint-commits:
    # Note: To re-run `lint-commits` after fixing the PR title, close-and-reopen the PR.
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
      - name: Check PR title
        run: |
          node "$GITHUB_WORKSPACE/.github/workflows/lintcommit.js"
